// pages/dealer-apply/dealer-apply.js
import promotionApi from '../../api/promotion.js';

Page({
  data: {
    formData: {
      companyName: '',
      contactPerson: '',
      contactPhone: '',
      businessLicense: '',
      schoolAuthorization: '',
      address: '',
      description: ''
    },
    submitting: false
  },

  onLoad() {
    wx.setNavigationBarTitle({
      title: '申请成为经销商'
    });
  },

  // 输入框变化
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 上传营业执照
  onUploadLicense() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        
        // 这里应该上传到服务器，现在只是模拟
        wx.showLoading({
          title: '上传中...'
        });
        
        setTimeout(() => {
          wx.hideLoading();
          this.setData({
            'formData.businessLicense': tempFilePath
          });
          wx.showToast({
            title: '上传成功',
            icon: 'success'
          });
        }, 1500);
      }
    });
  },

  // 上传学校授权书
  onUploadAuthorization() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        
        // 这里应该上传到服务器，现在只是模拟
        wx.showLoading({
          title: '上传中...'
        });
        
        setTimeout(() => {
          wx.hideLoading();
          this.setData({
            'formData.schoolAuthorization': tempFilePath
          });
          wx.showToast({
            title: '上传成功',
            icon: 'success'
          });
        }, 1500);
      }
    });
  },

  // 预览图片
  onPreviewImage(e) {
    const { url } = e.currentTarget.dataset;
    wx.previewImage({
      current: url,
      urls: [url]
    });
  },

  // 删除图片
  onDeleteImage(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [`formData.${field}`]: ''
    });
  },

  // 提交申请
  async onSubmit() {
    if (!this.validateForm()) return;

    try {
      this.setData({ submitting: true });
      
      const app = getApp();
      const res = await promotionApi.applyDealer(this.data.formData, { mock: app.globalData.mockMode });
      
      if (res.code === 0) {
        wx.showModal({
          title: '申请提交成功',
          content: '您的经销商申请已提交，我们将在3-5个工作日内完成审核，请耐心等待。',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
      }
    } catch (error) {
      console.error('提交申请失败:', error);
      wx.showToast({
        title: '提交失败',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  // 表单验证
  validateForm() {
    const { formData } = this.data;
    
    if (!formData.companyName.trim()) {
      wx.showToast({
        title: '请填写公司名称',
        icon: 'none'
      });
      return false;
    }
    
    if (!formData.contactPerson.trim()) {
      wx.showToast({
        title: '请填写联系人',
        icon: 'none'
      });
      return false;
    }
    
    if (!formData.contactPhone.trim()) {
      wx.showToast({
        title: '请填写联系电话',
        icon: 'none'
      });
      return false;
    }
    
    // 简单的手机号验证
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(formData.contactPhone)) {
      wx.showToast({
        title: '请填写正确的手机号',
        icon: 'none'
      });
      return false;
    }
    
    if (!formData.businessLicense) {
      wx.showToast({
        title: '请上传营业执照',
        icon: 'none'
      });
      return false;
    }
    
    if (!formData.schoolAuthorization) {
      wx.showToast({
        title: '请上传学校授权书',
        icon: 'none'
      });
      return false;
    }
    
    if (!formData.address.trim()) {
      wx.showToast({
        title: '请填写公司地址',
        icon: 'none'
      });
      return false;
    }
    
    return true;
  }
});
