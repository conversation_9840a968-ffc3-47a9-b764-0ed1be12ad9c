<!-- pages/dealer-apply/dealer-apply.wxml -->
<view class="page">
  <view class="apply-content">
    <!-- 申请说明 -->
    <view class="info-card">
      <view class="info-header">
        <text class="info-icon">📋</text>
        <text class="info-title">申请说明</text>
      </view>
      <view class="info-content">
        <text class="info-text">• 成为经销商后，您可以管理商品、处理订单、发展分销员</text>
        <text class="info-text">• 需要提供营业执照和学校授权书等资质文件</text>
        <text class="info-text">• 审核时间通常为3-5个工作日</text>
        <text class="info-text">• 审核通过后，您将获得经销商工作台权限</text>
      </view>
    </view>

    <!-- 申请表单 -->
    <view class="form-card">
      <view class="form-section">
        <text class="section-title">基本信息</text>
        
        <view class="form-item">
          <text class="form-label">公司名称 *</text>
          <input class="form-input" placeholder="请输入公司名称" value="{{formData.companyName}}" bindinput="onInputChange" data-field="companyName" />
        </view>
        
        <view class="form-item">
          <text class="form-label">联系人 *</text>
          <input class="form-input" placeholder="请输入联系人姓名" value="{{formData.contactPerson}}" bindinput="onInputChange" data-field="contactPerson" />
        </view>
        
        <view class="form-item">
          <text class="form-label">联系电话 *</text>
          <input class="form-input" type="number" placeholder="请输入联系电话" value="{{formData.contactPhone}}" bindinput="onInputChange" data-field="contactPhone" />
        </view>
        
        <view class="form-item">
          <text class="form-label">公司地址 *</text>
          <textarea class="form-textarea" placeholder="请输入详细地址" value="{{formData.address}}" bindinput="onInputChange" data-field="address"></textarea>
        </view>
      </view>

      <view class="form-section">
        <text class="section-title">资质文件</text>
        
        <!-- 营业执照 -->
        <view class="form-item">
          <text class="form-label">营业执照 *</text>
          <view class="upload-section">
            <view wx:if="{{formData.businessLicense}}" class="image-preview">
              <image class="preview-image" src="{{formData.businessLicense}}" mode="aspectFill" bindtap="onPreviewImage" data-url="{{formData.businessLicense}}"></image>
              <view class="image-actions">
                <text class="action-btn" bindtap="onDeleteImage" data-field="businessLicense">删除</text>
              </view>
            </view>
            <view wx:else class="upload-placeholder" bindtap="onUploadLicense">
              <text class="upload-icon">📷</text>
              <text class="upload-text">点击上传营业执照</text>
            </view>
          </view>
        </view>
        
        <!-- 学校授权书 -->
        <view class="form-item">
          <text class="form-label">学校授权书 *</text>
          <view class="upload-section">
            <view wx:if="{{formData.schoolAuthorization}}" class="image-preview">
              <image class="preview-image" src="{{formData.schoolAuthorization}}" mode="aspectFill" bindtap="onPreviewImage" data-url="{{formData.schoolAuthorization}}"></image>
              <view class="image-actions">
                <text class="action-btn" bindtap="onDeleteImage" data-field="schoolAuthorization">删除</text>
              </view>
            </view>
            <view wx:else class="upload-placeholder" bindtap="onUploadAuthorization">
              <text class="upload-icon">📷</text>
              <text class="upload-text">点击上传学校授权书</text>
            </view>
          </view>
        </view>
      </view>

      <view class="form-section">
        <text class="section-title">补充说明</text>
        
        <view class="form-item">
          <text class="form-label">申请说明</text>
          <textarea class="form-textarea" placeholder="请简要说明申请理由和经营计划（选填）" value="{{formData.description}}" bindinput="onInputChange" data-field="description"></textarea>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button class="btn btn-primary submit-btn" bindtap="onSubmit" disabled="{{submitting}}">
        {{submitting ? '提交中...' : '提交申请'}}
      </button>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>
