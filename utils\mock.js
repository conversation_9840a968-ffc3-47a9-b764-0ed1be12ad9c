// utils/mock.js
// Mock数据管理模块

import { MOCK_DATA_TEMPLATES, ERROR_TEMPLATES, addMockTemplate, getAllTemplates } from './mock-templates.js';

// Mock数据延迟配置
const MOCK_DELAY = {
  min: 300,  // 最小延迟300ms
  max: 800   // 最大延迟800ms
};

// 生成随机延迟
const getRandomDelay = () => {
  return Math.floor(Math.random() * (MOCK_DELAY.max - MOCK_DELAY.min + 1)) + MOCK_DELAY.min;
};



// Mock工具类
const MockUtil = {
  // 检查是否启用Mock
  isMockEnabled(config) {
    // 检查请求配置中的mock标志
    return config.custom && config.custom.mock === true;
  },

  // 输出Mock日志
  log(...args) {
    console.log('Mock:', ...args);
  },

  // 根据URL获取Mock数据
  getMockData(url, method = 'GET') {
    // 提取API路径（去除域名和查询参数）
    const apiPath = this.extractApiPath(url);

    this.log('Generating data for API path:', apiPath);

    // 检查是否有预定义的Mock数据
    if (MOCK_DATA_TEMPLATES[apiPath]) {
      const template = MOCK_DATA_TEMPLATES[apiPath];
      // 深拷贝模板数据，避免修改原始模板
      return JSON.parse(JSON.stringify(template));
    }

    // 如果没有预定义的Mock数据，返回通用成功响应
    return {
      code: 0,
      msg: '操作成功',
      data: {
        message: 'Mock数据 - ' + apiPath,
        timestamp: Date.now(),
        method: method
      }
    };
  },

  // 提取API路径
  extractApiPath(url) {
    try {
      // 移除域名部分
      const urlObj = new URL(url);
      let path = urlObj.pathname;
      
      // 如果路径包含 /app-api，则移除这个前缀
      if (path.startsWith('/app-api')) {
        path = path.substring(8);
      }
      
      return path;
    } catch (e) {
      // 如果URL解析失败，尝试简单的字符串处理
      const parts = url.split('/');
      const apiIndex = parts.findIndex(part => part === 'app-api');
      if (apiIndex !== -1 && apiIndex < parts.length - 1) {
        return '/' + parts.slice(apiIndex + 1).join('/').split('?')[0];
      }
      return url.split('?')[0]; // 移除查询参数
    }
  },

  // 生成Mock响应
  generateMockResponse(config) {
    return new Promise((resolve) => {
      const delay = getRandomDelay();

      setTimeout(() => {
        const mockData = this.getMockData(config.url, config.method);

        // 模拟微信小程序的响应格式
        const response = {
          data: mockData,
          statusCode: 200,
          header: {
            'content-type': 'application/json'
          },
          config: config
        };

        this.log('Response generated for', config.url, mockData);
        resolve(response);
      }, delay);
    });
  },

  // 生成Mock错误响应
  generateMockError(statusCode = 500) {
    return new Promise((resolve, reject) => {
      const delay = getRandomDelay();
      
      setTimeout(() => {
        const errorData = ERROR_TEMPLATES[statusCode] || ERROR_TEMPLATES[500];
        reject({
          statusCode: statusCode,
          errMsg: errorData.msg,
          data: errorData
        });
      }, delay);
    });
  },

  // 添加新的Mock数据模板
  addMockTemplate(apiPath, template) {
    addMockTemplate(apiPath, template);
    this.log('Added template for', apiPath);
  },

  // 获取所有Mock模板
  getAllTemplates() {
    return getAllTemplates();
  },

  // 设置Mock延迟范围
  setDelayRange(min, max) {
    MOCK_DELAY.min = min;
    MOCK_DELAY.max = max;
  }
};

export default MockUtil;
