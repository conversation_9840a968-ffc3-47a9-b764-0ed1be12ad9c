/* pages/logistics/logistics.wxss */
.page {
  background: linear-gradient(180deg, var(--gray-50) 0%, var(--background) 100%);
  min-height: 100vh;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-16) 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--gray-200);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

.loading-text {
  color: var(--text-muted);
  font-size: var(--text-sm);
}

.logistics-content {
  padding: var(--space-4);
}

/* 状态卡片 */
.status-card {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin-bottom: var(--space-4);
  color: var(--text-white);
  box-shadow: var(--shadow-lg);
}

.status-header {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-5);
}

.status-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-icon {
  font-size: 48rpx;
}

.status-info {
  flex: 1;
}

.status-text {
  display: block;
  font-size: var(--text-xl);
  font-weight: 700;
  margin-bottom: var(--space-2);
}

.status-desc {
  display: block;
  font-size: var(--text-base);
  opacity: 0.9;
}

.logistics-summary {
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
}

.summary-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-3) 0;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.summary-item:last-child {
  border-bottom: none;
}

.summary-label {
  font-size: var(--text-sm);
  opacity: 0.8;
}

.summary-value-wrapper {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.summary-value {
  font-size: var(--text-base);
  font-weight: 600;
}

.copy-btn, .contact-btn {
  font-size: var(--text-xs);
  background: rgba(255, 255, 255, 0.2);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

/* 时间轴卡片 */
.timeline-card {
  background: var(--surface);
  border-radius: var(--radius-xl);
  margin-bottom: var(--space-4);
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-5);
  background: linear-gradient(135deg, var(--surface) 0%, var(--gray-50) 100%);
  border-bottom: 1rpx solid var(--border-color);
}

.card-icon {
  font-size: 32rpx;
  margin-right: var(--space-3);
}

.card-title {
  flex: 1;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.refresh-btn {
  font-size: var(--text-sm);
  color: var(--primary-color);
  font-weight: 500;
}

.timeline-content {
  padding: var(--space-5);
}

.timeline-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-4);
  padding-bottom: var(--space-5);
  position: relative;
}

.timeline-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 20rpx;
  top: 40rpx;
  bottom: -20rpx;
  width: 2rpx;
  background: var(--border-color);
}

.timeline-item.current .timeline-dot {
  background: var(--primary-color);
  box-shadow: 0 0 0 6rpx rgba(99, 102, 241, 0.2);
}

.timeline-dot {
  width: 20rpx;
  height: 20rpx;
  background: var(--gray-300);
  border-radius: var(--radius-full);
  flex-shrink: 0;
  margin-top: 8rpx;
}

.timeline-content-wrapper {
  flex: 1;
}

.timeline-info {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--space-3);
  margin-bottom: var(--space-2);
}

.timeline-desc {
  flex: 1;
  font-size: var(--text-base);
  color: var(--text-primary);
  line-height: 1.5;
}

.timeline-time {
  font-size: var(--text-sm);
  color: var(--text-muted);
  white-space: nowrap;
}

.timeline-location {
  margin-top: var(--space-2);
}

.location-text {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  background: var(--gray-50);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
}

/* 收货信息卡片 */
.delivery-card {
  background: var(--surface);
  border-radius: var(--radius-xl);
  margin-bottom: var(--space-4);
  box-shadow: var(--shadow-md);
  overflow: hidden;
}

.delivery-content {
  padding: var(--space-5);
}

.delivery-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: var(--space-3) 0;
  border-bottom: 1rpx solid var(--border-color);
}

.delivery-item:last-child {
  border-bottom: none;
}

.delivery-label {
  font-size: var(--text-base);
  color: var(--text-secondary);
  width: 120rpx;
  flex-shrink: 0;
}

.delivery-value {
  flex: 1;
  font-size: var(--text-base);
  color: var(--text-primary);
  text-align: right;
  line-height: 1.5;
}

/* 错误状态 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-16) var(--space-4);
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: var(--space-4);
  opacity: 0.3;
}

.error-text {
  font-size: var(--text-lg);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.error-hint {
  font-size: var(--text-sm);
  color: var(--text-muted);
  margin-bottom: var(--space-6);
}

.retry-btn {
  width: 200rpx;
  height: 72rpx;
}

/* 安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
}

/* 动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
