// pages/cart/cart.js
import productApi from '../../api/product.js';

Page({
  data: {
    cartItems: [],
    selectedItems: [],
    totalAmount: 0,
    allSelected: false,
    loading: true,
    editMode: false
  },

  onLoad() {
    wx.setNavigationBarTitle({
      title: '购物车'
    });
  },

  onShow() {
    this.loadCartData();
  },

  // 加载购物车数据
  async loadCartData() {
    try {
      this.setData({ loading: true });
      const app = getApp();
      const res = await productApi.getCartList({ mock: app.globalData.mockMode });
      
      if (res.code === 0) {
        const cartItems = res.data.list || [];
        const selectedItems = cartItems.filter(item => item.selected).map(item => item.id);
        
        this.setData({
          cartItems: cartItems,
          selectedItems: selectedItems,
          loading: false
        });
        
        this.calculateTotal();
        this.checkAllSelected();
      }
    } catch (error) {
      console.error('加载购物车失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 选择商品
  onSelectItem(e) {
    const itemId = e.currentTarget.dataset.id;
    const { selectedItems } = this.data;
    const index = selectedItems.indexOf(itemId);
    
    if (index > -1) {
      selectedItems.splice(index, 1);
    } else {
      selectedItems.push(itemId);
    }
    
    this.setData({ selectedItems });
    this.calculateTotal();
    this.checkAllSelected();
  },

  // 全选/取消全选
  onSelectAll() {
    const { allSelected, cartItems } = this.data;
    
    if (allSelected) {
      this.setData({
        selectedItems: [],
        allSelected: false
      });
    } else {
      const allItemIds = cartItems.map(item => item.id);
      this.setData({
        selectedItems: allItemIds,
        allSelected: true
      });
    }
    
    this.calculateTotal();
  },

  // 检查是否全选
  checkAllSelected() {
    const { selectedItems, cartItems } = this.data;
    const allSelected = cartItems.length > 0 && selectedItems.length === cartItems.length;
    this.setData({ allSelected });
  },

  // 计算总金额
  calculateTotal() {
    const { cartItems, selectedItems } = this.data;
    const total = cartItems.reduce((sum, item) => {
      if (selectedItems.includes(item.id)) {
        return sum + (item.price * item.quantity);
      }
      return sum;
    }, 0);
    
    this.setData({ totalAmount: total });
  },

  // 修改数量
  async onQuantityChange(e) {
    const { id, type } = e.currentTarget.dataset;
    const { cartItems } = this.data;
    const item = cartItems.find(item => item.id === id);
    
    if (!item) return;
    
    let newQuantity = item.quantity;
    if (type === 'minus' && newQuantity > 1) {
      newQuantity--;
    } else if (type === 'plus') {
      newQuantity++;
    }
    
    if (newQuantity !== item.quantity) {
      try {
        const app = getApp();
        await productApi.updateCartItem({
          id: id,
          quantity: newQuantity
        }, { mock: app.globalData.mockMode });
        
        // 更新本地数据
        item.quantity = newQuantity;
        this.setData({ cartItems });
        this.calculateTotal();
        
      } catch (error) {
        console.error('更新数量失败:', error);
        wx.showToast({
          title: '更新失败',
          icon: 'none'
        });
      }
    }
  },

  // 删除商品
  async onDeleteItem(e) {
    const itemId = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个商品吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            const app = getApp();
            await productApi.removeCartItem(itemId, { mock: app.globalData.mockMode });
            
            // 更新本地数据
            const { cartItems, selectedItems } = this.data;
            const newCartItems = cartItems.filter(item => item.id !== itemId);
            const newSelectedItems = selectedItems.filter(id => id !== itemId);
            
            this.setData({
              cartItems: newCartItems,
              selectedItems: newSelectedItems
            });
            
            this.calculateTotal();
            this.checkAllSelected();
            
            wx.showToast({
              title: '删除成功',
              icon: 'success'
            });
            
          } catch (error) {
            console.error('删除失败:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 切换编辑模式
  onToggleEditMode() {
    this.setData({
      editMode: !this.data.editMode
    });
  },

  // 批量删除
  onBatchDelete() {
    const { selectedItems } = this.data;
    
    if (selectedItems.length === 0) {
      wx.showToast({
        title: '请选择要删除的商品',
        icon: 'none'
      });
      return;
    }
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除选中的${selectedItems.length}个商品吗？`,
      success: (res) => {
        if (res.confirm) {
          // 这里应该调用批量删除接口
          const { cartItems } = this.data;
          const newCartItems = cartItems.filter(item => !selectedItems.includes(item.id));
          
          this.setData({
            cartItems: newCartItems,
            selectedItems: []
          });
          
          this.calculateTotal();
          this.checkAllSelected();
          
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });
        }
      }
    });
  },

  // 去结算
  onCheckout() {
    const { selectedItems, cartItems } = this.data;
    
    if (selectedItems.length === 0) {
      wx.showToast({
        title: '请选择要结算的商品',
        icon: 'none'
      });
      return;
    }
    
    // 跳转到订单提交页
    wx.navigateTo({
      url: '/pages/order-submit/order-submit?type=cart'
    });
  },

  // 继续购物
  onContinueShopping() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  }
});
