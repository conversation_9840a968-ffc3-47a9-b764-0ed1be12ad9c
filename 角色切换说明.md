# 用户角色切换说明

## 概述
本项目支持两种用户角色：
- **customer**: 家长/学生角色（默认）
- **dealer**: 经销商角色

## 切换方法

### 方法一：直接修改Mock数据（推荐）

打开 `utils/mock-templates.js` 文件，找到 `/member/user/get` 接口的mock数据，修改以下字段：

```javascript
// 获取用户详情
'/member/user/get': {
  code: 0,
  msg: '获取成功',
  data: {
    // ... 其他字段
    role: 'dealer',        // 改为 'dealer' 切换为经销商
    dealerStatus: 'approved', // 改为 'approved' 表示审核通过
    // ... 其他字段
  }
}
```

### 方法二：使用工具函数

在小程序的任意页面的控制台中执行：

```javascript
// 切换为经销商（审核通过）
import { mockUtils } from '../utils/mock-templates.js';
mockUtils.switchUserRole('dealer', 'approved');

// 切换回普通用户
mockUtils.switchUserRole('customer', 'none');
```

### 方法三：在页面中添加切换按钮

在个人中心页面添加一个隐藏的切换按钮（仅开发环境使用）：

```javascript
// 在页面的js文件中添加
import { mockUtils } from '../../utils/mock-templates.js';

// 切换角色的方法
switchRole() {
  const currentRole = this.data.userInfo.role;
  if (currentRole === 'customer') {
    mockUtils.switchUserRole('dealer', 'approved');
    wx.showToast({
      title: '已切换为经销商',
      icon: 'success'
    });
  } else {
    mockUtils.switchUserRole('customer', 'none');
    wx.showToast({
      title: '已切换为普通用户',
      icon: 'success'
    });
  }
  
  // 重新加载用户信息
  this.loadUserInfo();
}
```

## 角色差异

### 普通用户 (customer)
- 可以浏览商品
- 可以下单购买
- 可以查看订单
- 可以申请成为经销商

### 经销商 (dealer)
- 拥有普通用户的所有功能
- 可以查看经销商管理页面
- 可以管理商品
- 可以查看推广数据
- 可以获得推广佣金

## 经销商状态说明

- **none**: 未申请经销商
- **pending**: 经销商申请审核中
- **approved**: 经销商审核通过
- **rejected**: 经销商申请被拒绝

## 注意事项

1. 角色切换后需要重新加载相关页面才能看到效果
2. 切换角色只在Mock模式下有效
3. 生产环境中角色由后端接口返回，不能随意切换
4. 建议在开发调试时使用角色切换功能

## 快速测试

1. 默认进入应用为普通用户
2. 修改mock数据切换为经销商
3. 重新进入个人中心页面
4. 应该能看到经销商相关的功能入口
