/* pages/product-detail/product-detail.wxss */
.page {
  background-color: var(--background);
  min-height: 100vh;
  padding-bottom: 160rpx;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-16) 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--gray-200);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

.loading-text {
  color: var(--text-muted);
  font-size: var(--text-sm);
}

/* 图片画廊 */
.image-gallery {
  position: relative;
  background-color: var(--surface);
  margin-bottom: var(--space-4);
}

.gallery-swiper {
  width: 100%;
  height: 750rpx;
}

.gallery-image {
  width: 100%;
  height: 100%;
}

.image-indicator {
  position: absolute;
  bottom: var(--space-4);
  right: var(--space-4);
  background: rgba(0, 0, 0, 0.6);
  color: var(--text-white);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
}

/* 商品信息卡片 */
.product-info-card {
  background: var(--surface);
  padding: var(--space-6);
  margin: 0 var(--space-4) var(--space-4);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-5);
}

.product-title {
  flex: 1;
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.4;
  margin-right: var(--space-4);
}

.product-status-wrapper {
  flex-shrink: 0;
}

.price-section {
  margin-bottom: var(--space-5);
}

.price-group {
  display: flex;
  align-items: baseline;
  gap: var(--space-3);
}

.current-price {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--danger-color);
}

.original-price {
  font-size: var(--text-lg);
  color: var(--text-muted);
  text-decoration: line-through;
}

.presale-price {
  display: flex;
  align-items: baseline;
  gap: var(--space-2);
  margin-bottom: var(--space-2);
}

.deposit-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.deposit-price {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--warning-color);
}

.final-price {
  display: flex;
  align-items: baseline;
  gap: var(--space-2);
}

.final-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.final-amount {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.product-meta {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.sales-count,
.stock-info {
  font-size: var(--text-sm);
  color: var(--text-muted);
}

/* 选择卡片 */
.selection-card {
  background: var(--surface);
  margin: 0 var(--space-4) var(--space-4);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
}

.detail-card {
  background: var(--surface);
  margin: 0 var(--space-4) var(--space-4);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
}

.card-header {
  margin-bottom: var(--space-5);
}

.card-title {
  display: block;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.card-subtitle {
  display: block;
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

/* 尺码选择 */
.size-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--space-3);
}

.size-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-4);
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--surface);
  transition: all 0.2s ease;
}

.size-option.selected {
  border-color: var(--primary-color);
  background: rgba(99, 102, 241, 0.05);
}

.size-option.disabled {
  opacity: 0.5;
  background: var(--gray-50);
}

.size-label {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.size-stock {
  font-size: var(--text-xs);
  color: var(--text-muted);
}

.size-option.disabled .size-stock {
  color: var(--danger-color);
}

/* 数量选择 */
.quantity-wrapper {
  display: flex;
  justify-content: center;
}

.quantity-controls {
  display: flex;
  align-items: center;
  background: var(--gray-50);
  border-radius: var(--radius-full);
  padding: var(--space-1);
}

.quantity-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: var(--radius-full);
  background: var(--surface);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
}

.quantity-btn:active {
  transform: scale(0.95);
}

.quantity-btn[disabled] {
  opacity: 0.5;
  background: var(--gray-100);
}

.quantity-btn.minus {
  margin-right: var(--space-4);
}

.quantity-btn.plus {
  margin-left: var(--space-4);
}

.btn-icon {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--primary-color);
}

.quantity-display {
  min-width: 120rpx;
  text-align: center;
}

.quantity-number {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
}

/* 商品详情 */
.detail-content {
  margin-top: var(--space-4);
}

.description-text {
  font-size: var(--text-base);
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-6);
}

.specifications-section {
  border-top: 1rpx solid var(--border-color);
  padding-top: var(--space-5);
}

.spec-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.spec-grid {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.spec-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3) 0;
  border-bottom: 1rpx solid var(--border-color);
}

.spec-row:last-child {
  border-bottom: none;
}

.spec-label {
  font-size: var(--text-base);
  color: var(--text-secondary);
  font-weight: 500;
}

.spec-value {
  font-size: var(--text-base);
  color: var(--text-primary);
}

/* 底部操作栏 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--surface);
  border-top: 1rpx solid var(--border-color);
  padding: var(--space-4);
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.action-buttons {
  display: flex;
  gap: var(--space-3);
  max-width: 750rpx;
  margin: 0 auto;
}

.btn-cart {
  flex: 1;
  height: 88rpx;
}

.btn-buy {
  flex: 2;
  height: 88rpx;
}

.btn-text {
  font-size: var(--text-base);
  font-weight: 600;
}

/* 安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
}
