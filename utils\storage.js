// utils/storage.js

// 存储键名常量
const STORAGE_KEYS = {
  TOKEN: 'token',
  REFRESH_TOKEN: 'refresh-token',
  USER_INFO: 'userInfo',
  USER_DETAILS: 'userDetails',
  LOGS: 'logs'
};

// 存储操作工具类
const StorageUtil = {
  // 设置存储项
  setItem(key, value) {
    try {
      wx.setStorageSync(key, value);
      return true;
    } catch (e) {
      console.error('Error setting storage item for key:', key, e);
      return false;
    }
  },
  
  // 获取存储项
  getItem(key) {
    try {
      return wx.getStorageSync(key);
    } catch (e) {
      console.error('Error getting storage item for key:', key, e);
      return null;
    }
  },
  
  // 移除存储项
  removeItem(key) {
    try {
      wx.removeStorageSync(key);
      return true;
    } catch (e) {
      console.error('Error removing storage item for key:', key, e);
      return false;
    }
  },
  
  // 清除所有存储
  clearAll() {
    try {
      wx.clearStorageSync();
      return true;
    } catch (e) {
      console.error('Error clearing storage:', e);
      return false;
    }
  },
  
  // 特定于应用程序的存储方法
  setToken(token) {
    return this.setItem(STORAGE_KEYS.TOKEN, token);
  },
  
  getToken() {
    return this.getItem(STORAGE_KEYS.TOKEN);
  },
  
  setRefreshToken(refreshToken) {
    return this.setItem(STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
  },
  
  getRefreshToken() {
    return this.getItem(STORAGE_KEYS.REFRESH_TOKEN);
  },
  
  setUserInfo(userInfo) {
    return this.setItem(STORAGE_KEYS.USER_INFO, userInfo);
  },
  
  getUserInfo() {
    return this.getItem(STORAGE_KEYS.USER_INFO);
  },
  
  setUserDetails(userDetails) {
    return this.setItem(STORAGE_KEYS.USER_DETAILS, userDetails);
  },
  
  getUserDetails() {
    return this.getItem(STORAGE_KEYS.USER_DETAILS);
  },
  
  setLogs(logs) {
    return this.setItem(STORAGE_KEYS.LOGS, logs);
  },
  
  getLogs() {
    return this.getItem(STORAGE_KEYS.LOGS);
  },
  
  // 移除所有与用户相关的数据
  clearUserData() {
    this.removeItem(STORAGE_KEYS.TOKEN);
    this.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
    this.removeItem(STORAGE_KEYS.USER_INFO);
    this.removeItem(STORAGE_KEYS.USER_DETAILS);
  }
};

export default StorageUtil;
