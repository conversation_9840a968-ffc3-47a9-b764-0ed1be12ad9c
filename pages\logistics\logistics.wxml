<!-- pages/logistics/logistics.wxml -->
<view class="page">
  <view wx:if="{{loading}}" class="loading-state">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <view wx:else-if="{{logistics}}" class="logistics-content">
    <!-- 物流状态卡片 -->
    <view class="status-card">
      <view class="status-header">
        <view class="status-icon-wrapper">
          <text class="status-icon">🚚</text>
        </view>
        <view class="status-info">
          <text class="status-text">{{logistics.status}}</text>
          <text class="status-desc">{{logistics.statusDesc}}</text>
        </view>
      </view>
      
      <view class="logistics-summary">
        <view class="summary-item">
          <text class="summary-label">快递公司</text>
          <text class="summary-value">{{logistics.company}}</text>
        </view>
        <view class="summary-item">
          <text class="summary-label">运单号</text>
          <view class="summary-value-wrapper">
            <text class="summary-value">{{trackingNo}}</text>
            <text class="copy-btn" bindtap="onCopyTrackingNo">复制</text>
          </view>
        </view>
        <view class="summary-item" wx:if="{{logistics.courierName}}">
          <text class="summary-label">快递员</text>
          <view class="summary-value-wrapper">
            <text class="summary-value">{{logistics.courierName}}</text>
            <text class="contact-btn" bindtap="onContactCourier">联系</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 物流轨迹 -->
    <view class="timeline-card">
      <view class="card-header">
        <text class="card-icon">📍</text>
        <text class="card-title">物流轨迹</text>
        <text class="refresh-btn" bindtap="onRefresh">刷新</text>
      </view>
      
      <view class="timeline-content">
        <view class="timeline-item {{index === 0 ? 'current' : ''}}" wx:for="{{logistics.timeline}}" wx:key="index">
          <view class="timeline-dot"></view>
          <view class="timeline-content-wrapper">
            <view class="timeline-info">
              <text class="timeline-desc">{{item.desc}}</text>
              <text class="timeline-time">{{formatTime(item.time)}}</text>
            </view>
            <view class="timeline-location" wx:if="{{item.location}}">
              <text class="location-text">{{item.location}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 收货信息 -->
    <view class="delivery-card" wx:if="{{logistics.deliveryInfo}}">
      <view class="card-header">
        <text class="card-icon">📦</text>
        <text class="card-title">收货信息</text>
      </view>
      
      <view class="delivery-content">
        <view class="delivery-item">
          <text class="delivery-label">收货人</text>
          <text class="delivery-value">{{logistics.deliveryInfo.recipient}}</text>
        </view>
        <view class="delivery-item">
          <text class="delivery-label">联系电话</text>
          <text class="delivery-value">{{logistics.deliveryInfo.phone}}</text>
        </view>
        <view class="delivery-item">
          <text class="delivery-label">收货地址</text>
          <text class="delivery-value">{{logistics.deliveryInfo.address}}</text>
        </view>
      </view>
    </view>
  </view>

  <view wx:else class="error-state">
    <text class="error-icon">📦</text>
    <text class="error-text">暂无物流信息</text>
    <text class="error-hint">请稍后再试或联系客服</text>
    <button class="btn btn-primary retry-btn" bindtap="onRefresh">重新加载</button>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>
