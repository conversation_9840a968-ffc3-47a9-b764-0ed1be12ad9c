// utils/mock-templates.js
// Mock数据模板

// 生成随机ID
const generateId = () => {
  return Math.random().toString(36).substring(2, 11);
};

// 生成随机手机号
const generateMobile = () => {
  const prefixes = ['138', '139', '150', '151', '152', '158', '159', '188', '189'];
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
  const suffix = Math.floor(Math.random() * 100000000).toString().padStart(8, '0');
  return prefix + suffix;
};

// Mock数据模板
export const MOCK_DATA_TEMPLATES = {
  // 社交登录
  '/member/auth/social-login': {
    code: 0,
    msg: '登录成功',
    data: {
      accessToken: 'mock_access_token_' + generateId(),
      refreshToken: 'mock_refresh_token_' + generateId(),
      expiresIn: 7200,
      tokenType: 'Bearer',
      user: {
        id: Math.floor(Math.random() * 10000),
        nickname: 'Mock用户' + Math.floor(Math.random() * 1000),
        avatar: 'https://via.placeholder.com/100x100?text=Avatar',
        mobile: generateMobile()
      }
    }
  },

  // 刷新Token
  '/member/auth/refresh-token': {
    code: 0,
    msg: '刷新成功',
    data: {
      accessToken: 'mock_new_access_token_' + generateId(),
      refreshToken: 'mock_new_refresh_token_' + generateId(),
      expiresIn: 7200,
      tokenType: 'Bearer'
    }
  },

  // 获取用户详情
  '/member/user/get': {
    code: 0,
    msg: '获取成功',
    data: {
      id: Math.floor(Math.random() * 10000),
      nickname: 'Mock用户' + Math.floor(Math.random() * 1000),
      avatar: 'https://via.placeholder.com/100x100?text=Avatar',
      mobile: generateMobile(),
      email: 'mock' + Math.floor(Math.random() * 1000) + '@example.com',
      point: Math.floor(Math.random() * 10000),
      experience: Math.floor(Math.random() * 50000),
      role: 'customer', // customer: 家长/学生, dealer: 经销商
      dealerStatus: 'none', // none: 未申请, pending: 审核中, approved: 已通过, rejected: 已拒绝
      level: {
        id: Math.floor(Math.random() * 10) + 1,
        name: 'LV' + (Math.floor(Math.random() * 10) + 1),
        icon: 'https://via.placeholder.com/50x50?text=LV'
      },
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString(),
      status: 1,
      gender: Math.floor(Math.random() * 3), // 0:未知, 1:男, 2:女
      birthday: '1990-01-01',
      city: '北京市',
      province: '北京',
      country: '中国'
    }
  },

  // 用户资料更新
  '/member/profile/update': {
    code: 0,
    msg: '更新成功',
    data: {
      id: Math.floor(Math.random() * 10000),
      nickname: '更新后的昵称' + Math.floor(Math.random() * 100),
      avatar: 'https://via.placeholder.com/100x100?text=Updated',
      updateTime: new Date().toISOString()
    }
  },

  // 订单列表
  '/member/orders/list': {
    code: 0,
    msg: '获取成功',
    data: {
      list: Array.from({ length: 10 }, (_, i) => ({
        id: 1000 + i,
        orderNo: 'ORDER' + Date.now() + i,
        amount: Math.floor(Math.random() * 10000) / 100,
        status: Math.floor(Math.random() * 4) + 1,
        createTime: new Date(Date.now() - Math.random() * 86400000 * 30).toISOString(),
        products: Array.from({ length: Math.floor(Math.random() * 3) + 1 }, (_, j) => ({
          id: 100 + j,
          name: '商品' + (j + 1),
          price: Math.floor(Math.random() * 1000) / 100,
          quantity: Math.floor(Math.random() * 5) + 1
        }))
      })),
      total: 100,
      pageNum: 1,
      pageSize: 10
    }
  },

  // 设置信息
  '/member/settings/get': {
    code: 0,
    msg: '获取成功',
    data: {
      notifications: {
        push: true,
        email: false,
        sms: true
      },
      privacy: {
        showProfile: true,
        showActivity: false
      },
      theme: 'light',
      language: 'zh-CN'
    }
  },

  // 校服相关API Mock数据
  // 首页数据
  '/api/home/<USER>': {
    code: 0,
    msg: '获取成功',
    data: {
      dealer: {
        name: 'XX校服专营店',
        phone: '138****8888',
        address: '广东省深圳市南山区科技园'
      },
      school: {
        name: 'XX中学',
        season: '2024年春季校服'
      },
      distributor: {
        name: '张老师',
        code: 'ABC123'
      },
      banners: [
        {
          id: 1,
          image: 'https://via.placeholder.com/750x300?text=Banner1',
          link: ''
        },
        {
          id: 2,
          image: 'https://via.placeholder.com/750x300?text=Banner2',
          link: ''
        }
      ]
    }
  },

  // 商品列表
  '/api/products/list': {
    code: 0,
    msg: '获取成功',
    data: {
      list: [
        {
          id: 1,
          name: '春季校服套装',
          price: 128.00,
          originalPrice: 158.00,
          image: 'https://via.placeholder.com/300x300?text=春季校服',
          status: 'in_stock', // in_stock: 现货, pre_sale: 预售
          stock: 50,
          sales: 128,
          description: '优质面料，舒适透气，适合春季穿着'
        },
        {
          id: 2,
          name: '夏季运动服',
          price: 100.00,
          deposit: 20.00,
          finalPayment: 80.00,
          image: 'https://via.placeholder.com/300x300?text=夏季运动服',
          status: 'pre_sale',
          presaleTarget: 100,
          presaleCurrent: 25,
          description: '透气速干，运动首选'
        }
      ],
      total: 2,
      pageNum: 1,
      pageSize: 10
    }
  },

  // 商品详情
  '/api/products/detail': {
    code: 0,
    msg: '获取成功',
    data: {
      id: 1,
      name: '春季校服套装',
      price: 128.00,
      originalPrice: 158.00,
      images: [
        'https://via.placeholder.com/750x750?text=商品图1',
        'https://via.placeholder.com/750x750?text=商品图2',
        'https://via.placeholder.com/750x750?text=商品图3'
      ],
      status: 'in_stock',
      stock: 50,
      sales: 128,
      description: '优质面料，舒适透气，适合春季穿着。采用环保染料，色彩持久不褪色。',
      sizes: [
        { size: 'S', stock: 10 },
        { size: 'M', stock: 15 },
        { size: 'L', stock: 8 },
        { size: 'XL', stock: 5 }
      ],
      specifications: [
        { name: '面料', value: '纯棉' },
        { name: '颜色', value: '藏青色' },
        { name: '适用季节', value: '春季' }
      ]
    }
  },

  // 购物车列表
  '/api/cart/list': {
    code: 0,
    msg: '获取成功',
    data: {
      list: [
        {
          id: 1,
          productId: 1,
          name: '春季校服套装',
          image: 'https://via.placeholder.com/300x300?text=春季校服',
          price: 128.00,
          size: 'M',
          quantity: 1,
          selected: true,
          isPreSale: false
        },
        {
          id: 2,
          productId: 2,
          name: '夏季运动服',
          image: 'https://via.placeholder.com/300x300?text=夏季运动服',
          price: 20.00, // 预售定金
          size: 'L',
          quantity: 1,
          selected: true,
          isPreSale: true
        }
      ],
      totalAmount: 148.00
    }
  },

  // 订单提交
  '/api/orders/submit': {
    code: 0,
    msg: '订单提交成功',
    data: {
      orderId: 'ORDER' + Date.now(),
      orderNo: '202401150001',
      paymentUrl: 'mock://payment/url'
    }
  },

  // 订单列表
  '/api/orders/list': {
    code: 0,
    msg: '获取成功',
    data: {
      list: [
        {
          id: 1,
          orderNo: '202401150001',
          status: 'wait_final_payment', // wait_payment: 待付款, paid: 已付款, wait_final_payment: 待付尾款, shipping: 已发货, completed: 已完成
          totalAmount: 100.00,
          paidAmount: 20.00,
          createTime: '2024-01-15 10:30:00',
          products: [
            {
              id: 2,
              name: '夏季运动服',
              image: 'https://via.placeholder.com/300x300?text=夏季运动服',
              price: 20.00,
              size: 'M',
              quantity: 1,
              isPreSale: true
            }
          ]
        },
        {
          id: 2,
          orderNo: '202401150002',
          status: 'shipping',
          totalAmount: 256.00,
          paidAmount: 256.00,
          createTime: '2024-01-14 15:20:00',
          trackingNo: 'SF1234567890',
          products: [
            {
              id: 1,
              name: '春季校服套装',
              image: 'https://via.placeholder.com/300x300?text=春季校服',
              price: 128.00,
              size: 'L',
              quantity: 2
            }
          ]
        }
      ],
      total: 2,
      pageNum: 1,
      pageSize: 10
    }
  },

  // 订单详情
  '/api/orders/detail': {
    code: 0,
    msg: '获取成功',
    data: {
      id: 1,
      orderNo: '202401150001',
      status: 'shipping',
      totalAmount: 148.00,
      paidAmount: 28.00,
      finalPayment: 120.00,
      isPreSale: true,
      createTime: '2024-01-15 10:30:00',
      payTime: '2024-01-15 10:35:00',
      shipTime: '2024-01-18 14:20:00',
      address: {
        id: 1,
        name: '张同学',
        phone: '138****8888',
        province: '北京市',
        city: '北京市',
        district: '朝阳区',
        address: '某某小区某某号楼某某单元某某室'
      },
      products: [
        {
          id: 1,
          name: '春季校服套装',
          image: 'https://via.placeholder.com/300x300?text=春季校服',
          price: 128.00,
          size: 'M',
          quantity: 1,
          isPreSale: false
        },
        {
          id: 2,
          name: '夏季运动服（预售）',
          image: 'https://via.placeholder.com/300x300?text=夏季运动服',
          price: 20.00,
          size: 'L',
          quantity: 1,
          isPreSale: true
        }
      ],
      trackingNo: 'SF1234567890',
      expressCompany: '顺丰速运',
      progress: [
        { status: '订单确认', time: '2024-01-15 10:30', completed: true },
        { status: '开始生产', time: '2024-01-20 09:00', completed: true },
        { status: '生产中', time: '2024-01-25 16:00', completed: true },
        { status: '质量检验', time: '2024-01-26 10:00', completed: true },
        { status: '已发货', time: '2024-01-18 14:20', completed: true }
      ]
    }
  },

  // 收货地址列表
  '/api/address/list': {
    code: 0,
    msg: '获取成功',
    data: {
      list: [
        {
          id: 1,
          name: '张三',
          phone: '13800138000',
          province: '广东省',
          city: '深圳市',
          district: '南山区',
          address: '科技园南区',
          isDefault: true
        },
        {
          id: 2,
          name: '李四',
          phone: '13900139000',
          province: '广东省',
          city: '广州市',
          district: '天河区',
          address: '珠江新城',
          isDefault: false
        }
      ]
    }
  },

  // 经销商工作台数据
  '/api/dealer/dashboard': {
    code: 0,
    msg: '获取成功',
    data: {
      todayData: {
        newOrders: 15,
        sales: 3200.00,
        presaleRate: 85
      },
      pendingTasks: {
        presaleOrders: 5,
        stockOrders: 3,
        distributorApproval: 2
      },
      certification: {
        status: 'pending', // pending: 待认证, approved: 已认证, rejected: 已拒绝
        message: '请完成营业执照和学校授权书上传'
      }
    }
  },

  // 经销商商品管理
  '/api/dealer/products': {
    code: 0,
    msg: '获取成功',
    data: {
      list: [
        {
          id: 1,
          name: '春季校服套装',
          image: 'https://via.placeholder.com/300x300?text=春季校服',
          type: 'stock', // stock: 现货, presale: 预售
          price: 128.00,
          stock: 50,
          sales: 128,
          status: 'selling' // selling: 销售中, offline: 已下架, pending: 待开始
        },
        {
          id: 2,
          name: '夏季运动服',
          image: 'https://via.placeholder.com/300x300?text=夏季运动服',
          type: 'presale',
          deposit: 20.00,
          finalPrice: 100.00,
          presaleTarget: 100,
          presaleCurrent: 25,
          status: 'pending'
        }
      ],
      total: 2
    }
  },

  // 经销商订单管理
  '/api/dealer/orders': {
    code: 0,
    msg: '获取成功',
    data: {
      list: [
        {
          id: 1,
          orderNo: '202401150001',
          customerName: '张三',
          customerPhone: '138****8888',
          productName: '夏季运动服 M码',
          amount: 20.00,
          status: 'paid_deposit',
          type: 'presale',
          createTime: '2024-01-15 10:30:00'
        },
        {
          id: 2,
          orderNo: '202401150002',
          customerName: '李四',
          customerPhone: '139****9999',
          productName: '春季校服套装 L码',
          amount: 256.00,
          status: 'shipped',
          type: 'stock',
          trackingNo: 'SF1234567890',
          createTime: '2024-01-14 15:20:00'
        }
      ],
      total: 2
    }
  },

  // 分销管理数据
  '/api/dealer/distribution': {
    code: 0,
    msg: '获取成功',
    data: {
      statistics: {
        distributorCount: 12,
        totalCommission: 1280.00,
        promotionOrders: 68
      },
      commissionSettings: {
        level1Rate: 8,
        level2Rate: 3
      },
      distributors: [
        {
          id: 1,
          name: '张老师',
          phone: '138****8888',
          code: 'ABC123',
          orders: 15,
          commission: 320.00,
          status: 'approved'
        },
        {
          id: 2,
          name: '李家长',
          phone: '139****9999',
          code: 'DEF456',
          orders: 8,
          commission: 160.00,
          status: 'pending'
        }
      ]
    }
  },

  // 用户推广数据
  '/api/user/promotion': {
    code: 0,
    msg: '获取成功',
    data: {
      code: 'ABC123',
      qrCode: 'https://via.placeholder.com/200x200?text=QRCode',
      statistics: {
        totalOrders: 15,
        totalCommission: 320.00,
        thisMonthOrders: 5,
        thisMonthCommission: 80.00
      },
      orders: [
        {
          id: 1,
          orderNo: '202401150001',
          customerName: '张***',
          amount: 128.00,
          commission: 10.24,
          createTime: '2024-01-15 10:30:00'
        }
      ]
    }
  },

  // 经销商申请
  '/api/dealer/apply': {
    code: 0,
    msg: '申请提交成功',
    data: {
      applicationId: 'APP' + Date.now(),
      status: 'pending',
      message: '您的申请已提交，我们将在3-5个工作日内完成审核'
    }
  },

  // 推广数据
  '/api/promotion/data': {
    code: 0,
    msg: '获取成功',
    data: {
      code: 'PROMO' + Math.floor(Math.random() * 10000),
      statistics: {
        totalOrders: Math.floor(Math.random() * 100),
        totalCommission: Math.floor(Math.random() * 5000),
        thisMonthOrders: Math.floor(Math.random() * 20)
      },
      commissionRate: 5.0,
      withdrawableAmount: Math.floor(Math.random() * 1000)
    }
  }
};

// 错误响应模板
export const ERROR_TEMPLATES = {
  400: { code: 400, msg: '请求参数错误' },
  401: { code: 401, msg: '未授权访问' },
  403: { code: 403, msg: '禁止访问' },
  404: { code: 404, msg: '接口不存在' },
  500: { code: 500, msg: '服务器内部错误' }
};

// 添加新模板的函数
export const addMockTemplate = (apiPath, template) => {
  MOCK_DATA_TEMPLATES[apiPath] = template;
};

// 获取所有模板
export const getAllTemplates = () => {
  return { ...MOCK_DATA_TEMPLATES };
};

// 工具函数导出
export const mockUtils = {
  generateId,
  generateMobile
};
