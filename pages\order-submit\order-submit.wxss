/* pages/order-submit/order-submit.wxss */
.page {
  background: linear-gradient(180deg, var(--gray-50) 0%, var(--background) 100%);
  min-height: 100vh;
  padding-bottom: 140rpx;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-16) 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--gray-200);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

.loading-text {
  color: var(--text-muted);
  font-size: var(--text-sm);
}

.order-content {
  padding: var(--space-3) var(--space-4);
}

/* 卡片通用样式 */
.section-card {
  background: var(--surface);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-4);
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--border-color);
  overflow: hidden;
  transition: all 0.3s ease;
}

.section-card:active {
  transform: translateY(-2rpx);
  box-shadow: var(--shadow-xl);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) var(--space-5);
  background: linear-gradient(135deg, var(--surface) 0%, var(--gray-50) 100%);
  border-bottom: 1rpx solid var(--border-color);
}

.header-left {
  display: flex;
  align-items: center;
}

.icon-wrapper {
  width: 64rpx;
  height: 64rpx;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-4);
}

.icon-wrapper.address-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.icon-wrapper.product-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.icon-wrapper.payment-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.section-icon {
  font-size: 28rpx;
  color: var(--text-white);
}

.section-title {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--text-primary);
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.action-text {
  font-size: var(--text-base);
  color: var(--primary-color);
  font-weight: 600;
}

.arrow-icon {
  font-size: var(--text-lg);
  color: var(--primary-color);
  font-weight: bold;
}

/* 地址卡片 */
.address-content {
  padding: var(--space-4) var(--space-5);
}

.recipient-info {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-3);
}

.recipient-name {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.recipient-phone {
  font-size: var(--text-base);
  color: var(--text-white);
  background: var(--primary-color);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-full);
  font-weight: 500;
}

.address-text {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  line-height: 1.6;
  background: var(--gray-50);
  padding: var(--space-5);
  border-radius: var(--radius-lg);
  border-left: 6rpx solid var(--primary-color);
}

.address-empty {
  padding: var(--space-8) var(--space-6);
}

.empty-content {
  display: flex;
  align-items: center;
  gap: var(--space-5);
}

.empty-icon {
  font-size: 80rpx;
  opacity: 0.3;
}

.empty-text-group {
  flex: 1;
}

.empty-title {
  display: block;
  font-size: var(--text-xl);
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: var(--space-2);
}

.empty-subtitle {
  display: block;
  font-size: var(--text-base);
  color: var(--text-muted);
}

/* 商品信息 */
.product-list {
  padding: var(--space-4) var(--space-5);
}

.product-item {
  display: flex;
  gap: var(--space-3);
  padding: var(--space-3);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-3);
  border: 1rpx solid var(--border-color);
}

.product-item:last-child {
  margin-bottom: 0;
}

.product-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: var(--radius-md);
  flex-shrink: 0;
  box-shadow: var(--shadow-sm);
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-main {
  flex: 1;
}

.product-name {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.4;
  margin-bottom: var(--space-2);
}

.product-specs {
  display: flex;
  gap: var(--space-2);
}

.spec-tag {
  background: var(--surface);
  color: var(--text-secondary);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: 500;
  border: 1rpx solid var(--border-color);
}

.product-price {
  margin-top: var(--space-2);
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.price-group {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.price-badge {
  background: var(--warning-color);
  color: var(--text-white);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
}

.price-value {
  font-size: var(--text-lg);
  font-weight: 700;
  color: var(--danger-color);
}

/* 费用明细卡片 */
.summary-card {
  background: var(--surface);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-4);
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--border-color);
  overflow: hidden;
}

.card-header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4) var(--space-5);
  background: linear-gradient(135deg, var(--surface) 0%, var(--gray-50) 100%);
  border-bottom: 1rpx solid var(--border-color);
}

.card-icon {
  font-size: 32rpx;
}

.card-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.summary-content {
  padding: var(--space-4) var(--space-5);
}

.summary-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-3);
}

.summary-row:last-child {
  margin-bottom: 0;
}

.summary-label {
  font-size: var(--text-base);
  color: var(--text-secondary);
  font-weight: 500;
}

.summary-value {
  font-size: var(--text-base);
  color: var(--text-primary);
  font-weight: 600;
}

.summary-value.free {
  color: var(--success-color);
}

.summary-divider {
  height: 1rpx;
  background: var(--border-color);
  margin: var(--space-4) 0;
}

.summary-total {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: var(--space-3);
  border-top: 2rpx solid var(--border-color);
}

.total-label {
  font-size: var(--text-lg);
  color: var(--text-primary);
  font-weight: 600;
}

.total-amount {
  font-size: var(--text-2xl);
  color: var(--danger-color);
  font-weight: 700;
}

/* 支付方式 */
.payment-options {
  padding: var(--space-4) var(--space-5);
}

.payment-option {
  display: block;
  margin-bottom: var(--space-4);
  border-radius: var(--radius-lg);
  overflow: hidden;
  border: 2rpx solid var(--border-color);
  transition: all 0.2s ease;
}

.payment-option:last-child {
  margin-bottom: 0;
}

.payment-option.selected {
  border-color: var(--primary-color);
  background: rgba(99, 102, 241, 0.05);
}

.payment-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-5);
}

.payment-left {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.payment-icon-wrapper {
  width: 64rpx;
  height: 64rpx;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
}

.payment-icon-wrapper.wechat {
  background: linear-gradient(135deg, #07c160 0%, #05a050 100%);
}

.payment-icon-wrapper.alipay {
  background: linear-gradient(135deg, #1677ff 0%, #0958d9 100%);
}

.payment-icon-text {
  font-size: 28rpx;
  color: var(--text-white);
}

.payment-name {
  font-size: var(--text-lg);
  color: var(--text-primary);
  font-weight: 600;
}

/* 协议卡片 */
.agreement-card {
  background: var(--surface);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-4);
  box-shadow: var(--shadow-md);
  border: 1rpx solid var(--border-color);
  overflow: hidden;
}

.agreement-content {
  padding: var(--space-4) var(--space-5);
}

.agreement-points {
  margin-bottom: var(--space-4);
}

.agreement-point {
  display: block;
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-2);
}

.agreement-point:last-child {
  margin-bottom: 0;
}

.agreement-checkbox {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  border: 1rpx solid var(--border-color);
}

.checkbox-text {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.agreement-link {
  font-size: var(--text-sm);
  color: var(--primary-color);
  text-decoration: underline;
}

/* 底部提交栏 */
.submit-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, var(--surface) 0%, var(--gray-50) 100%);
  border-top: 1rpx solid var(--border-color);
  padding: var(--space-4);
  z-index: 1000;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.submit-content {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  max-width: 750rpx;
  margin: 0 auto;
}

.amount-section {
  flex: 1;
  background: var(--surface);
  border-radius: var(--radius-lg);
  padding: var(--space-3);
  border: 1rpx solid var(--border-color);
}

.amount-row {
  display: flex;
  align-items: baseline;
  justify-content: space-between;
}

.amount-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

.amount-value {
  font-size: var(--text-xl);
  color: var(--danger-color);
  font-weight: 700;
}

.submit-button {
  min-width: 200rpx;
  height: 80rpx;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border: none;
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-lg);
  transition: all 0.2s ease;
}

.submit-button:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-md);
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
}

.button-text {
  font-size: var(--text-base);
  color: var(--text-white);
  font-weight: 600;
}

.button-icon {
  font-size: var(--text-lg);
  color: var(--text-white);
  font-weight: bold;
}

/* 安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
}