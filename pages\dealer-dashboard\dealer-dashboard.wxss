/* pages/dealer-dashboard/dealer-dashboard.wxss */
.container {
  background-color: #f8f8f8;
  min-height: 100vh;
}

.loading {
  text-align: center;
  padding: 200rpx 0;
  color: #666;
}

.dashboard-content {
  padding: 30rpx;
}

/* 认证提醒 */
.certification-alert {
  background-color: #fff3cd;
  border: 2rpx solid #ffeaa7;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.alert-content {
  display: flex;
  align-items: center;
}

.alert-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
}

.alert-text {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.alert-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.alert-message {
  font-size: 28rpx;
  color: #666;
}

.alert-btn {
  width: 120rpx;
  height: 60rpx;
  background-color: #ffc107;
  color: #333;
  font-size: 24rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

/* 数据卡片 */
.data-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  color: white;
  box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.3);
}

.card-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  text-align: center;
}

.data-stats {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 快捷入口 */
.quick-actions {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid #dee2e6;
}

.action-item:active {
  background-color: #e9ecef;
}

.action-icon {
  font-size: 48rpx;
  margin-bottom: 15rpx;
}

.action-title {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

/* 待处理事项 */
.pending-tasks {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.task-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.task-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
}

.task-text {
  font-size: 28rpx;
  color: #333;
}
