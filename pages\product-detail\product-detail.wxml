<!-- pages/product-detail/product-detail.wxml -->
<view class="page">
  <view wx:if="{{loading}}" class="loading-state">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <view wx:else-if="{{product}}" class="product-detail">
    <!-- 商品图片轮播 -->
    <view class="image-gallery">
      <swiper class="gallery-swiper" indicator-dots="{{true}}" indicator-color="rgba(255,255,255,0.5)" indicator-active-color="#ffffff" bindchange="onImageChange">
        <swiper-item wx:for="{{product.images}}" wx:key="*this">
          <image class="gallery-image" src="{{item}}" mode="aspectFill" bindtap="onImagePreview" data-src="{{item}}"></image>
        </swiper-item>
      </swiper>
      <view class="image-indicator">
        <text>{{currentImageIndex + 1}} / {{product.images.length}}</text>
      </view>
    </view>

    <!-- 商品信息卡片 -->
    <view class="product-info-card">
      <view class="product-header">
        <text class="product-title">{{product.name}}</text>
        <view class="product-status-wrapper">
          <view wx:if="{{product.status === 'in_stock'}}" class="tag tag-success">现货充足</view>
          <view wx:else class="tag tag-warning">预售商品</view>
        </view>
      </view>

      <view class="price-section">
        <view wx:if="{{product.status === 'in_stock'}}" class="price-group">
          <text class="current-price">¥{{product.price}}</text>
          <text wx:if="{{product.originalPrice}}" class="original-price">¥{{product.originalPrice}}</text>
        </view>
        <view wx:else class="price-group">
          <view class="presale-price">
            <text class="deposit-label">定金</text>
            <text class="deposit-price">¥{{product.deposit}}</text>
          </view>
          <view class="final-price">
            <text class="final-label">尾款</text>
            <text class="final-amount">¥{{product.finalPayment}}</text>
          </view>
        </view>
      </view>

      <view class="product-meta">
        <text class="sales-count">已售 {{product.sales || 0}} 件</text>
        <view wx:if="{{product.stock}}" class="stock-info">
          <text>库存 {{product.stock}} 件</text>
        </view>
      </view>
    </view>

    <!-- 尺码选择卡片 -->
    <view class="selection-card">
      <view class="card-header">
        <text class="card-title">选择尺码</text>
        <text class="card-subtitle">请选择合适的尺码</text>
      </view>
      <view class="size-grid">
        <view
          class="size-option {{selectedSize === item.size ? 'selected' : ''}} {{item.stock === 0 ? 'disabled' : ''}}"
          wx:for="{{product.sizes}}"
          wx:key="size"
          bindtap="onSizeSelect"
          data-size="{{item.size}}"
        >
          <text class="size-label">{{item.size}}</text>
          <text class="size-stock">{{item.stock > 0 ? '库存' + item.stock : '缺货'}}</text>
        </view>
      </view>
    </view>

    <!-- 数量选择卡片 -->
    <view class="selection-card">
      <view class="card-header">
        <text class="card-title">选择数量</text>
      </view>
      <view class="quantity-wrapper">
        <view class="quantity-controls">
          <button class="quantity-btn minus" bindtap="onQuantityMinus" disabled="{{quantity <= 1}}">
            <text class="btn-icon">−</text>
          </button>
          <view class="quantity-display">
            <text class="quantity-number">{{quantity}}</text>
          </view>
          <button class="quantity-btn plus" bindtap="onQuantityPlus">
            <text class="btn-icon">+</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 商品详情卡片 -->
    <view class="detail-card">
      <view class="card-header">
        <text class="card-title">商品详情</text>
      </view>
      <view class="detail-content">
        <text class="description-text">{{product.description}}</text>

        <!-- 商品规格 -->
        <view wx:if="{{product.specifications}}" class="specifications-section">
          <text class="spec-title">商品规格</text>
          <view class="spec-grid">
            <view class="spec-row" wx:for="{{product.specifications}}" wx:key="name">
              <text class="spec-label">{{item.name}}</text>
              <text class="spec-value">{{item.value}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="action-bar" wx:if="{{product}}">
    <view class="action-buttons">
      <button class="btn btn-secondary btn-cart" bindtap="onAddToCart">
        <text class="btn-text">加入购物车</text>
      </button>
      <button class="btn btn-primary btn-buy" bindtap="onBuyNow">
        <text class="btn-text">立即购买</text>
      </button>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>
