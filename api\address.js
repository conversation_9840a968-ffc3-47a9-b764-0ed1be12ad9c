// api/address.js
import http from '../utils/http';

const addressApi = {
  // 获取收货地址列表
  getAddressList(options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/address/list',
      method: 'GET',
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 获取地址详情
  getAddressDetail(addressId, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/address/detail',
      method: 'GET',
      data: { id: addressId },
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 添加收货地址
  addAddress(data, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/address/add',
      method: 'POST',
      data: data,
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 更新收货地址
  updateAddress(data, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/address/update',
      method: 'POST',
      data: data,
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 删除收货地址
  deleteAddress(addressId, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/address/delete',
      method: 'POST',
      data: { id: addressId },
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 设置默认地址
  setDefaultAddress(addressId, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/address/set-default',
      method: 'POST',
      data: { id: addressId },
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  }
};

export default addressApi;
