// pages/dealer-dashboard/dealer-dashboard.js
import dealerApi from '../../api/dealer.js';

Page({
  data: {
    dashboardData: null,
    loading: true
  },

  onLoad() {
    wx.setNavigationBarTitle({
      title: '经销商工作台'
    });
    this.loadDashboardData();
  },

  onShow() {
    this.loadDashboardData();
  },

  // 加载工作台数据
  async loadDashboardData() {
    try {
      this.setData({ loading: true });
      const res = await dealerApi.getDashboard({ mock: true });
      if (res.code === 0) {
        this.setData({
          dashboardData: res.data,
          loading: false
        });
      }
    } catch (error) {
      console.error('加载工作台数据失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 去认证
  onCertification() {
    wx.navigateTo({
      url: '/pages/dealer-certification/dealer-certification'
    });
  },

  // 商品管理
  onProductManage() {
    wx.navigateTo({
      url: '/pages/product-manage/product-manage'
    });
  },

  // 订单处理
  onOrderManage() {
    wx.navigateTo({
      url: '/pages/order-manage/order-manage'
    });
  },

  // 分销管理
  onDistributionManage() {
    wx.navigateTo({
      url: '/pages/distribution-manage/distribution-manage'
    });
  },

  // 数据统计
  onDataStatistics() {
    wx.navigateTo({
      url: '/pages/data-statistics/data-statistics'
    });
  },

  // 客户管理
  onCustomerManage() {
    wx.navigateTo({
      url: '/pages/customer-manage/customer-manage'
    });
  },

  // 财务管理
  onFinanceManage() {
    wx.navigateTo({
      url: '/pages/finance-manage/finance-manage'
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadDashboardData().finally(() => {
      wx.stopPullDownRefresh();
    });
  }
});
