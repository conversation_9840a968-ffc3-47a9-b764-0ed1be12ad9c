<!-- pages/index/index.wxml -->
<view class="page">
  <!-- Hero Banner -->
  <view class="hero-section" wx:if="{{homeInfo && homeInfo.banners}}">
    <swiper class="hero-swiper" indicator-dots="{{true}}" indicator-color="rgba(255,255,255,0.3)" indicator-active-color="#ffffff" autoplay="{{true}}" interval="4000" duration="500" bindchange="onBannerChange">
      <swiper-item wx:for="{{homeInfo.banners}}" wx:key="id">
        <view class="hero-slide">
          <image class="hero-image" src="{{item.image}}" mode="aspectFill"></image>
          <view class="hero-overlay">
            <view class="hero-content">
              <text class="hero-title">优质校服</text>
              <text class="hero-subtitle">品质保证 · 舒适体验</text>
            </view>
          </view>
        </view>
      </swiper-item>
    </swiper>
  </view>

  <!-- 快速信息卡片 -->
  <view class="quick-info" wx:if="{{homeInfo}}">
    <view class="info-grid">
      <!-- 经销商信息 -->
      <view class="info-item">
        <view class="info-icon-wrapper dealer">
          <text class="info-icon">🏪</text>
        </view>
        <view class="info-text">
          <text class="info-label">经销商</text>
          <text class="info-value">{{homeInfo.dealer.name}}</text>
        </view>
      </view>

      <!-- 学校信息 -->
      <view class="info-item">
        <view class="info-icon-wrapper school">
          <text class="info-icon">🎓</text>
        </view>
        <view class="info-text">
          <text class="info-label">学校</text>
          <text class="info-value">{{homeInfo.school.name}}</text>
        </view>
      </view>

      <!-- 推荐人信息 -->
      <view class="info-item">
        <view class="info-icon-wrapper referrer">
          <text class="info-icon">👨‍🏫</text>
        </view>
        <view class="info-text">
          <text class="info-label">推荐人</text>
          <text class="info-value">{{homeInfo.distributor.name}}</text>
        </view>
      </view>
    </view>

    <!-- 优惠码卡片 -->
    <view class="promo-card" wx:if="{{homeInfo.distributor.code}}">
      <view class="promo-content">
        <view class="promo-left">
          <text class="promo-title">专属优惠码</text>
          <text class="promo-code">{{homeInfo.distributor.code}}</text>
        </view>
        <view class="promo-right">
          <text class="promo-badge">专享</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 商品展示区 -->
  <view class="products-section">
    <view class="section-header">
      <text class="section-title">精选商品</text>
      <text class="section-subtitle">为您精心挑选的优质校服</text>
    </view>

    <view wx:if="{{loading}}" class="loading-state">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <view wx:else class="products-grid">
      <view class="product-card card-hover" wx:for="{{products}}" wx:key="id" bindtap="onProductTap" data-id="{{item.id}}">
        <view class="product-image-wrapper">
          <image class="product-image" src="{{item.image}}" mode="aspectFill"></image>
          <view class="product-status-badge">
            <text wx:if="{{item.status === 'in_stock'}}" class="tag tag-success">现货</text>
            <text wx:else class="tag tag-warning">预售</text>
          </view>
        </view>

        <view class="product-content">
          <text class="product-name">{{item.name}}</text>

          <view class="product-price-section">
            <view wx:if="{{item.status === 'in_stock'}}" class="price-group">
              <text class="current-price">¥{{item.price}}</text>
              <text wx:if="{{item.originalPrice}}" class="original-price">¥{{item.originalPrice}}</text>
            </view>
            <view wx:else class="price-group">
              <text class="deposit-price">定金 ¥{{item.deposit}}</text>
              <text class="final-price">尾款 ¥{{item.finalPayment}}</text>
            </view>
          </view>

          <view class="product-meta">
            <text class="sales-count">已售 {{item.sales || 0}} 件</text>
            <view wx:if="{{item.status === 'pre_sale'}}" class="presale-progress">
              <text class="progress-text">{{item.presaleCurrent}}/{{item.presaleTarget}}</text>
            </view>
          </view>
        </view>

        <view class="product-actions">
          <button class="btn btn-primary btn-sm" bindtap="onAddToCart" data-product="{{item}}" catchtap="true">
            加入购物车
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 浮动购物车 -->
  <view class="floating-cart" bindtap="onCartTap">
    <view class="cart-button">
      <text class="cart-icon">🛒</text>
      <view wx:if="{{cartCount > 0}}" class="cart-count">{{cartCount}}</view>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>
