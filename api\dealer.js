// api/dealer.js
import http from '../utils/http';

const dealerApi = {
  // 获取经销商工作台数据
  getDashboard(options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/dealer/dashboard',
      method: 'GET',
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 获取经销商商品列表
  getProducts(params = {}, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/dealer/products',
      method: 'GET',
      data: params,
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 创建预售商品
  createPresale(data, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/dealer/products/create-presale',
      method: 'POST',
      data: data,
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 开始销售商品
  startSelling(productId, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/dealer/products/start-selling',
      method: 'POST',
      data: { productId: productId },
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 下架商品
  offlineProduct(productId, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/dealer/products/offline',
      method: 'POST',
      data: { productId: productId },
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 获取订单列表
  getOrders(params = {}, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/dealer/orders',
      method: 'GET',
      data: params,
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 导出生产订单
  exportProductionOrders(params = {}, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/dealer/orders/export-production',
      method: 'POST',
      data: params,
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 更新订单进度
  updateOrderProgress(orderId, progress, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/dealer/orders/update-progress',
      method: 'POST',
      data: { orderId: orderId, progress: progress },
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 批量发货
  batchShipping(orderIds, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/dealer/orders/batch-shipping',
      method: 'POST',
      data: { orderIds: orderIds },
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 获取分销管理数据
  getDistribution(options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/dealer/distribution',
      method: 'GET',
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 更新佣金设置
  updateCommissionSettings(data, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/dealer/distribution/commission',
      method: 'POST',
      data: data,
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 审核分销员
  approveDistributor(distributorId, status, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/dealer/distribution/approve',
      method: 'POST',
      data: { distributorId: distributorId, status: status },
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  }
};

export default dealerApi;
