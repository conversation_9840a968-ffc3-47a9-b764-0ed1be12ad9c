// api/promotion.js
import http from '../utils/http';

const promotionApi = {
  // 获取用户推广数据
  getPromotionData(options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/user/promotion',
      method: 'GET',
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 申请成为分销员
  applyDistributor(data, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/user/apply-distributor',
      method: 'POST',
      data: data,
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 申请成为经销商
  applyDealer(data, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/user/apply-dealer',
      method: 'POST',
      data: data,
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 获取推广订单列表
  getPromotionOrders(params = {}, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/user/promotion/orders',
      method: 'GET',
      data: params,
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 获取佣金明细
  getCommissionDetails(params = {}, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/user/promotion/commission',
      method: 'GET',
      data: params,
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 提现申请
  applyWithdraw(amount, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/user/promotion/withdraw',
      method: 'POST',
      data: { amount: amount },
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  }
};

export default promotionApi;
