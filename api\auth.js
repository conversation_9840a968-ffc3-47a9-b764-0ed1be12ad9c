// api/auth.js
import http from '../utils/http';

const authApi = {
  socialLogin(data, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/member/auth/social-login',
      method: 'POST',
      data: data,
      custom: {
        auth: false,
        mock: options.mock || false  // 支持Mock选项
      }
    });
  },

  refreshToken(refreshToken, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/member/auth/refresh-token?refreshToken=' + encodeURIComponent(refreshToken),
      method: 'POST',
      data: {},
      custom: {
        mock: options.mock || false  // 支持Mock选项
      }
    });
  }
};

export default authApi;
