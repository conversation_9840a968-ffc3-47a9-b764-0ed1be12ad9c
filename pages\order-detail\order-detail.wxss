/* pages/order-detail/order-detail.wxss */
.page {
  background-color: var(--background);
  min-height: 100vh;
  padding-bottom: 160rpx;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-16) 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--gray-200);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

.loading-text {
  color: var(--text-muted);
  font-size: var(--text-sm);
}

.order-detail {
  padding: var(--space-4);
}

/* 状态卡片 */
.status-card {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  margin-bottom: var(--space-5);
  color: var(--text-white);
  box-shadow: var(--shadow-lg);
}

.status-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-6);
}

.status-icon-wrapper {
  width: 120rpx;
  height: 120rpx;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-5);
  background: rgba(255, 255, 255, 0.2);
}

.status-icon-wrapper.warning {
  background: rgba(245, 158, 11, 0.2);
}

.status-icon-wrapper.success {
  background: rgba(16, 185, 129, 0.2);
}

.status-icon-wrapper.info {
  background: rgba(59, 130, 246, 0.2);
}

.status-icon-wrapper.danger {
  background: rgba(239, 68, 68, 0.2);
}

.status-icon {
  font-size: 60rpx;
}

.status-info {
  flex: 1;
}

.status-text {
  display: block;
  font-size: var(--text-2xl);
  font-weight: 700;
  margin-bottom: var(--space-2);
}

.status-desc {
  display: block;
  font-size: var(--text-base);
  opacity: 0.9;
}

/* 进度条 */
.progress-section {
  margin-top: var(--space-6);
}

.progress-steps {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
  z-index: 2;
}

.step-dot {
  width: 24rpx;
  height: 24rpx;
  border-radius: var(--radius-full);
  background: rgba(255, 255, 255, 0.3);
  margin-bottom: var(--space-2);
  transition: all 0.3s ease;
}

.step-item.active .step-dot {
  background: var(--text-white);
  box-shadow: 0 0 0 6rpx rgba(255, 255, 255, 0.3);
}

.step-text {
  font-size: var(--text-xs);
  opacity: 0.8;
}

.step-item.active .step-text {
  opacity: 1;
  font-weight: 600;
}

.step-line {
  position: absolute;
  top: 12rpx;
  left: 25%;
  right: 25%;
  height: 2rpx;
  background: rgba(255, 255, 255, 0.3);
  z-index: 1;
  transition: all 0.3s ease;
}

.step-line.active {
  background: var(--text-white);
}

/* 卡片通用样式 */
.address-card,
.product-card,
.order-info-card,
.summary-card {
  background: var(--surface);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-4);
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-color);
  overflow: hidden;
}

.card-header {
  display: flex;
  align-items: center;
  padding: var(--space-5);
  background: var(--gray-50);
  border-bottom: 1rpx solid var(--border-color);
}

.card-icon {
  font-size: 32rpx;
  margin-right: var(--space-3);
  opacity: 0.8;
}

.card-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

/* 地址内容 */
.address-content {
  padding: var(--space-5);
}

.address-info {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-3);
}

.recipient-name {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.recipient-phone {
  font-size: var(--text-base);
  color: var(--text-secondary);
  background: var(--gray-100);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
}

.address-detail {
  font-size: var(--text-base);
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 商品列表 */
.product-list {
  padding: var(--space-5);
}

.product-item {
  display: flex;
  gap: var(--space-4);
  padding: var(--space-4) 0;
  border-bottom: 1rpx solid var(--border-color);
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: var(--radius-md);
  flex-shrink: 0;
}

.product-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.product-name {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.4;
}

.product-specs {
  display: flex;
  gap: var(--space-4);
}

.spec-item {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.product-price {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-top: auto;
}

.price-label {
  font-size: var(--text-xs);
  color: var(--warning-color);
  background: rgba(245, 158, 11, 0.1);
  padding: 2rpx 8rpx;
  border-radius: var(--radius-sm);
  font-weight: 500;
}

.price-amount {
  font-size: var(--text-lg);
  color: var(--danger-color);
  font-weight: 600;
}

/* 订单信息 */
.order-info-content {
  padding: var(--space-5);
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4) 0;
  border-bottom: 1rpx solid var(--border-color);
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-size: var(--text-base);
  color: var(--text-secondary);
  font-weight: 500;
}

.info-value-wrapper {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.info-value {
  font-size: var(--text-base);
  color: var(--text-primary);
}

.copy-btn,
.track-btn {
  font-size: var(--text-sm);
  color: var(--primary-color);
  background: rgba(99, 102, 241, 0.1);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-weight: 500;
}

/* 费用明细 */
.summary-content {
  padding: var(--space-5);
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3) 0;
}

.summary-label {
  font-size: var(--text-base);
  color: var(--text-secondary);
}

.summary-value {
  font-size: var(--text-base);
  color: var(--text-primary);
  font-weight: 500;
}

.summary-value.free {
  color: var(--success-color);
}

.summary-value.highlight {
  color: var(--danger-color);
  font-weight: 600;
}

.summary-divider {
  height: 1rpx;
  background: var(--border-color);
  margin: var(--space-3) 0;
}

.summary-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4) 0;
  background: var(--gray-50);
  margin: 0 calc(0rpx - var(--space-5));
  padding-left: var(--space-5);
  padding-right: var(--space-5);
  border-radius: var(--radius-md);
}

.total-label {
  font-size: var(--text-lg);
  color: var(--text-primary);
  font-weight: 600;
}

.total-amount {
  font-size: var(--text-xl);
  color: var(--danger-color);
  font-weight: 700;
}

/* 底部操作栏 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--surface);
  border-top: 1rpx solid var(--border-color);
  padding: var(--space-4);
  z-index: 1000;
}

.action-buttons {
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
}

.action-buttons .btn {
  min-width: 160rpx;
  height: 80rpx;
  font-size: var(--text-base);
  font-weight: 500;
}

/* 安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
}