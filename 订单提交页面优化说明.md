# 订单提交页面优化说明

## 优化内容总结

### 1. 布局优化 - 提高内容密度

#### 间距调整
- **页面内边距**: 从 `var(--space-5)` 减少到 `var(--space-3)`
- **卡片间距**: 从 `var(--space-6)` 减少到 `var(--space-4)`
- **卡片内边距**: 从 `var(--space-6)` 减少到 `var(--space-4)` 或 `var(--space-5)`
- **商品项间距**: 从 `var(--space-5)` 减少到 `var(--space-3)`

#### 组件尺寸优化
- **商品图片**: 从 120rpx × 120rpx 减少到 100rpx × 100rpx
- **商品名称字体**: 从 `var(--text-lg)` 减少到 `var(--text-base)`
- **价格字体**: 从 `var(--text-2xl)` 减少到 `var(--text-lg)`
- **提交按钮**: 从 240rpx × 88rpx 减少到 200rpx × 80rpx

#### 圆角优化
- **卡片圆角**: 从 `var(--radius-xl)` 改为 `var(--radius-lg)`
- **商品项圆角**: 从 `var(--radius-lg)` 改为 `var(--radius-md)`

### 2. 费用明细UI优化

#### 新增样式类
```css
.summary-card          /* 费用明细卡片容器 */
.card-header          /* 卡片头部 */
.card-icon            /* 卡片图标 */
.card-title           /* 卡片标题 */
.summary-content      /* 明细内容区域 */
.summary-row          /* 明细行 */
.summary-label        /* 明细标签 */
.summary-value        /* 明细数值 */
.summary-divider      /* 分割线 */
.summary-total        /* 总计行 */
.total-label          /* 总计标签 */
.total-amount         /* 总计金额 */
```

#### 视觉效果
- 添加了渐变背景头部
- 清晰的分割线
- 突出显示总金额
- 免运费标签使用成功色

### 3. 协议部分UI优化

#### 新增样式类
```css
.agreement-card       /* 协议卡片 */
.agreement-content    /* 协议内容 */
.agreement-points     /* 协议要点列表 */
.agreement-point      /* 单个协议要点 */
.agreement-checkbox   /* 协议勾选框 */
.checkbox-text        /* 勾选框文字 */
.agreement-link       /* 协议链接 */
```

#### 视觉效果
- 协议要点使用项目符号
- 勾选框区域有背景色区分
- 协议链接有下划线样式

### 4. 页面跳转优化

#### 修改前
```javascript
// 跳转到订单详情页
wx.redirectTo({
  url: `/pages/order-detail/order-detail?id=${res.data.orderId}`
});
```

#### 修改后
```javascript
// 跳转到订单列表页
wx.redirectTo({
  url: '/pages/order-list/order-list'
});
```

#### 优势
- 用户可以看到所有订单
- 更符合用户习惯
- 避免直接跳转到单个订单详情

### 5. Mock数据优化

#### 新增订单提交接口
```javascript
'/api/orders/submit': {
  code: 0,
  msg: '订单提交成功',
  data: {
    orderId: 'ORDER' + Date.now(),
    orderNo: '202401' + Math.floor(Math.random() * 100000),
    status: 'wait_payment',
    totalAmount: 128.00,
    createTime: new Date().toISOString()
  }
}
```

### 6. 角色切换功能

#### 方法一：直接修改Mock数据
在 `utils/mock-templates.js` 中修改：
```javascript
role: 'dealer',        // 改为经销商
dealerStatus: 'approved' // 审核通过
```

#### 方法二：使用工具函数
```javascript
import { mockUtils } from '../utils/mock-templates.js';
mockUtils.switchUserRole('dealer', 'approved');
```

#### 角色状态说明
- **customer**: 普通用户（家长/学生）
- **dealer**: 经销商用户
- **dealerStatus**: none/pending/approved/rejected

### 7. 底部提交栏优化

#### 尺寸调整
- **页面底部间距**: 从 160rpx 减少到 140rpx
- **金额区域内边距**: 从 `var(--space-4)` 减少到 `var(--space-3)`
- **按钮尺寸**: 从 240rpx × 88rpx 减少到 200rpx × 80rpx

#### 对齐优化
- 确保"提交订单"文字居中对齐
- 优化按钮内容的flex布局

## 优化效果

### 内容密度提升
- 单屏可显示更多内容
- 减少滚动操作
- 提高信息获取效率

### 视觉体验改善
- 费用明细更加清晰美观
- 协议部分结构化展示
- 整体布局更加紧凑

### 用户体验优化
- 订单提交后跳转更合理
- 角色切换方便测试
- 页面响应更加流畅

## 注意事项

1. **兼容性**: 所有修改都保持了原有的响应式设计
2. **可维护性**: 使用CSS变量，便于后续调整
3. **测试建议**: 在不同屏幕尺寸下测试布局效果
4. **角色切换**: 仅在开发环境使用，生产环境需要后端支持
