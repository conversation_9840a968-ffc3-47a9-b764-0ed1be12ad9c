// api/order.js
import http from '../utils/http';

const orderApi = {
  // 提交订单
  submitOrder(data, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/orders/submit',
      method: 'POST',
      data: data,
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 获取订单列表
  getOrderList(params = {}, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/orders/list',
      method: 'GET',
      data: params,
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 获取订单详情
  getOrderDetail(orderId, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/orders/detail',
      method: 'GET',
      data: { id: orderId },
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 支付订单
  payOrder(orderId, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/orders/pay',
      method: 'POST',
      data: { orderId: orderId },
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 查询物流信息
  getLogistics(trackingNo, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/logistics/track',
      method: 'GET',
      data: { trackingNo: trackingNo },
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 支付尾款
  payFinalPayment(orderId, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/orders/pay-final',
      method: 'POST',
      data: { orderId: orderId },
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 确认收货
  confirmReceipt(orderId, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/orders/confirm',
      method: 'POST',
      data: { orderId: orderId },
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 取消订单
  cancelOrder(orderId, reason, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/orders/cancel',
      method: 'POST',
      data: { orderId: orderId, reason: reason },
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  }
};

export default orderApi;
