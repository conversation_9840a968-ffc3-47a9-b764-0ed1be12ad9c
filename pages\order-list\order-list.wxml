<!-- pages/order-list/order-list.wxml -->
<view class="page">
  <!-- 标签栏 -->
  <view class="tabs-container">
    <scroll-view class="tabs-scroll" scroll-x="{{true}}">
      <view class="tabs">
        <view class="tab-item {{currentTab === 'all' ? 'active' : ''}}" bindtap="onTabChange" data-tab="all">
          <text class="tab-text">全部</text>
        </view>
        <view class="tab-item {{currentTab === 'wait_payment' ? 'active' : ''}}" bindtap="onTabChange" data-tab="wait_payment">
          <text class="tab-text">待付款</text>
        </view>
        <view class="tab-item {{currentTab === 'wait_final_payment' ? 'active' : ''}}" bindtap="onTabChange" data-tab="wait_final_payment">
          <text class="tab-text">待付尾款</text>
        </view>
        <view class="tab-item {{currentTab === 'shipping' ? 'active' : ''}}" bindtap="onTabChange" data-tab="shipping">
          <text class="tab-text">已发货</text>
        </view>
        <view class="tab-item {{currentTab === 'completed' ? 'active' : ''}}" bindtap="onTabChange" data-tab="completed">
          <text class="tab-text">已完成</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 订单列表 -->
  <view class="order-content">
    <view wx:if="{{loading}}" class="loading-state">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <view wx:elif="{{orders.length === 0}}" class="empty-state">
      <text class="empty-icon">📋</text>
      <text class="empty-text">暂无订单</text>
      <text class="empty-hint">快去选购心仪的商品吧</text>
    </view>

    <view wx:else class="order-list">
      <view class="order-card" wx:for="{{orders}}" wx:key="id">
        <!-- 订单头部 -->
        <view class="order-header">
          <view class="order-info">
            <text class="order-no">订单号：{{item.orderNo}}</text>
            <text class="order-time">{{item.createTime}}</text>
          </view>
          <view class="order-status">
            <view class="tag {{getStatusClass(item.status)}}">{{getStatusText(item.status)}}</view>
          </view>
        </view>

        <!-- 商品列表 -->
        <view class="order-products" bindtap="onOrderDetail" data-id="{{item.id}}">
          <view class="product-item" wx:for="{{item.products}}" wx:key="id" wx:for-item="product">
            <image class="product-image" src="{{product.image}}" mode="aspectFill"></image>
            <view class="product-details">
              <text class="product-name">{{product.name}}</text>
              <view class="product-specs">
                <text class="spec-text">{{product.size}} | 数量: {{product.quantity}}</text>
              </view>
              <view class="product-price">
                <text wx:if="{{product.isPreSale}}" class="price-label">定金</text>
                <text class="price-amount">¥{{product.price}}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 订单金额 -->
        <view class="order-amount">
          <text class="amount-label">
            {{item.status === 'wait_final_payment' ? '已付定金' : '订单金额'}}
          </text>
          <text class="amount-value">¥{{item.paidAmount || item.totalAmount}}</text>
          <text wx:if="{{item.status === 'wait_final_payment'}}" class="final-payment">
            （尾款¥{{item.totalAmount - item.paidAmount}}）
          </text>
        </view>

        <!-- 订单操作 -->
        <view class="order-actions">
          <button wx:if="{{item.status === 'wait_payment'}}" class="btn btn-secondary btn-sm" bindtap="onCancelOrder" data-id="{{item.id}}" catchtap="true">
            取消订单
          </button>
          <button wx:if="{{item.status === 'wait_payment'}}" class="btn btn-primary btn-sm" bindtap="onPayOrder" data-id="{{item.id}}" catchtap="true">
            立即支付
          </button>
          
          <button wx:if="{{item.status === 'wait_final_payment'}}" class="btn btn-primary btn-sm" bindtap="onPayOrder" data-id="{{item.id}}" catchtap="true">
            支付尾款
          </button>
          
          <button wx:if="{{item.status === 'shipping'}}" class="btn btn-secondary btn-sm" bindtap="onOrderDetail" data-id="{{item.id}}" catchtap="true">
            查看物流
          </button>
          <button wx:if="{{item.status === 'shipping'}}" class="btn btn-primary btn-sm" bindtap="onConfirmReceipt" data-id="{{item.id}}" catchtap="true">
            确认收货
          </button>
          
          <button wx:if="{{item.status === 'completed'}}" class="btn btn-secondary btn-sm" bindtap="onOrderDetail" data-id="{{item.id}}" catchtap="true">
            查看详情
          </button>
          
          <button class="btn btn-ghost btn-sm" bindtap="onContactService" catchtap="true">
            联系客服
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>
