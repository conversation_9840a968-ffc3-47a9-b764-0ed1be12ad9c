# AI编码规范手册

## 项目概述

这是一个微信小程序项目，采用原生小程序开发框架，具备完整的用户认证、Token无感刷新、Mock数据支持等功能。

## 1. 项目结构和架构

### 1.1 目录结构
```
mini/
├── api/                    # API接口层
│   ├── auth.js            # 认证相关接口
│   └── user.js            # 用户相关接口
├── pages/                 # 页面文件
│   └── index/             # 首页
│       ├── index.js       # 页面逻辑
│       ├── index.wxml     # 页面结构
│       └── index.wxss     # 页面样式
├── store/                 # 状态管理
│   └── user.js           # 用户状态管理
├── utils/                 # 工具类
│   ├── http.js           # HTTP请求封装
│   ├── mock.js           # Mock功能
│   ├── mock-templates.js # Mock数据模板
│   └── storage.js        # 存储工具
├── app.js                # 应用入口
├── app.json              # 应用配置
└── app.wxss              # 全局样式
```

### 1.2 架构特点
- **分层架构**: API层 → 业务逻辑层 → 页面展示层
- **无感刷新**: 自动处理Token过期和刷新
- **Mock支持**: 开发阶段可使用Mock数据
- **状态管理**: 统一的用户状态管理
- **工具封装**: HTTP请求、存储等工具类封装

### 1.3 核心模块

#### HTTP请求模块 (`utils/http.js`)
- 提供 `http.fetch()` 方法进行API调用
- 自动处理Token认证和刷新
- 支持Mock模式切换
- 防重复请求机制

#### Mock系统 (`utils/mock.js` + `utils/mock-templates.js`)
- 非侵入式Mock数据支持
- 预定义常用API的Mock模板
- 支持自定义Mock数据

#### API接口层 (`api/`)
- 统一的接口调用封装
- 支持Mock参数传递
- 标准化的请求配置


### 2.3 页面布局规范

#### 标准页面结构
```xml
<!-- 标准页面模板 -->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">页面标题</text>
  </view>
  
  <!-- 页面内容 -->
  <view class="page-content">
    <!-- 内容区域 -->
  </view>
  
  <!-- 页面底部 -->
  <view class="page-footer">
    <!-- 底部操作区 -->
  </view>
</view>
```

## 3. API接口规范

### 3.1 接口调用方式


#### 方式：通过API模块调用（推荐）
```javascript
import userApi from '../api/user.js';

const result = await userApi.getUserDetails({ 
  mock: false  // 可选的Mock参数
});
```

### 3.2 新增API接口步骤

#### 步骤1：在对应API文件中添加接口方法
```javascript
// api/user.js
const userApi = {
  // 获取用户详情
  getUserDetails(options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/member/user/get',
      method: 'GET',
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },
  
  // 新增接口示例
  updateUserProfile(data, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/member/user/update',
      method: 'POST',
      data: data,
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  }
};
```

#### 步骤2：在Mock模板中添加对应数据
```javascript
// utils/mock-templates.js
export const MOCK_DATA_TEMPLATES = {
  // 新增Mock数据
  '/member/user/update': {
    code: 0,
    msg: '更新成功',
    data: {
      id: 12345,
      nickname: '更新后的昵称',
      updateTime: new Date().toISOString()
    }
  }
};
```


r
 
#### Mock数据格式规范
```javascript
{
  code: 0,                    // 状态码：0成功，非0失败
  msg: '操作成功',            // 提示信息
  data: {                     // 业务数据
    // 具体的业务数据字段
  }
}
```
