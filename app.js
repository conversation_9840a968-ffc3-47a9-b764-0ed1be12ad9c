import StorageUtil from './utils/storage';
import authApi from './api/auth';
import userApi from './api/user';

App({
  onLaunch() {
    // 初始化全局登录状态
    this.globalData.isLoggedIn = false;
    // 启动时自动登录
    this.autoLogin();
  },
  autoLogin() {
    wx.login({
      success: res => {
        if (res.code) {
          // 发送 code 到后台进行认证
          authApi.socialLogin({
            type: '34',
            code: res.code,
            state: 'default'
          }).then(response => {
            this.globalData.isLoggedIn = true;
            // Store tokens
            if (response.data && response.data.accessToken) {
              StorageUtil.setToken(response.data.accessToken);
              StorageUtil.setRefreshToken(response.data.refreshToken);
            }
            // 获取用户信息
            wx.getUserInfo({
              success: userRes => {
                this.globalData.userInfo = userRes.userInfo;
                StorageUtil.setUserInfo(userRes.userInfo);
              }
            });
            // Fetch detailed user information after login only if not already fetched
            if (!this.globalData.userDetails) {
              this.fetchUserDetails();
            }
}).catch(err => {
            console.error('Auto login failed:', err || 'Unknown error');
          });
        } else {
          console.error('wx.login failed:', res.errMsg);
        }
      },
      fail: err => {
        console.error('wx.login error:', err);
      }
    });
  },
  globalData: {
    apiDomain: 'https://api.wildpath.cn/app-api',
    userInfo: null,
    userDetails: null,
    isLoggedIn: false,
    isFetchingUserDetails: false,
    // Mock模式开关，开发阶段可以设置为true
    mockMode: true
  },
  fetchUserDetails() {
    // 检查是否已经有用户详情或正在获取中
    if (this.globalData.isFetchingUserDetails || this.globalData.userDetails) {
      return;
    }

    // 检查本地存储中是否已有用户详情
    const cachedUserDetails = StorageUtil.getUserDetails();
    if (cachedUserDetails) {
      this.globalData.userDetails = cachedUserDetails;
      return;
    }

    this.globalData.isFetchingUserDetails = true;
    userApi.getUserDetails().then(response => {
      this.globalData.userDetails = response.data;
      StorageUtil.setUserDetails(response.data);
    }).catch(err => {
      console.error('Failed to fetch user details in app.js:', err);
    }).finally(() => {
      this.globalData.isFetchingUserDetails = false;
    });
  }
})
