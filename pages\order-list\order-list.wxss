/* pages/order-list/order-list.wxss */
.page {
  background-color: var(--background);
  min-height: 100vh;
}

/* 标签栏 */
.tabs-container {
  background: var(--surface);
  border-bottom: 1rpx solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 100;
}

.tabs-scroll {
  white-space: nowrap;
}

.tabs {
  display: flex;
  padding: 0 var(--space-4);
}

.tab-item {
  flex-shrink: 0;
  padding: var(--space-4) var(--space-3);
  margin-right: var(--space-2);
  position: relative;
}

.tab-item.active {
  color: var(--primary-color);
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: var(--primary-color);
  border-radius: 2rpx;
}

.tab-text {
  font-size: var(--text-base);
  font-weight: 500;
  color: var(--text-secondary);
}

.tab-item.active .tab-text {
  color: var(--primary-color);
  font-weight: 600;
}

/* 内容区域 */
.order-content {
  padding: var(--space-4);
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-16) 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--gray-200);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

.loading-text {
  color: var(--text-muted);
  font-size: var(--text-sm);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-16) 0;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: var(--space-4);
  opacity: 0.3;
}

.empty-text {
  font-size: var(--text-lg);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.empty-hint {
  font-size: var(--text-sm);
  color: var(--text-muted);
}

/* 订单列表 */
.order-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.order-card {
  background: var(--surface);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4);
  border-bottom: 1rpx solid var(--border-color);
}

.order-info {
  flex: 1;
}

.order-no {
  display: block;
  font-size: var(--text-base);
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: var(--space-1);
}

.order-time {
  display: block;
  font-size: var(--text-sm);
  color: var(--text-muted);
}

.order-status {
  flex-shrink: 0;
}

/* 商品列表 */
.order-products {
  padding: var(--space-4);
}

.product-item {
  display: flex;
  gap: var(--space-3);
  padding: var(--space-3) 0;
  border-bottom: 1rpx solid var(--border-color);
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: var(--radius-md);
  flex-shrink: 0;
}

.product-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.product-name {
  font-size: var(--text-base);
  color: var(--text-primary);
  font-weight: 500;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-specs {
  display: flex;
  gap: var(--space-2);
}

.spec-text {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.product-price {
  display: flex;
  align-items: baseline;
  gap: var(--space-2);
  margin-top: auto;
}

.price-label {
  font-size: var(--text-xs);
  color: var(--warning-color);
  background: rgba(245, 158, 11, 0.1);
  padding: 2rpx 6rpx;
  border-radius: var(--radius-sm);
}

.price-amount {
  font-size: var(--text-base);
  color: var(--danger-color);
  font-weight: 600;
}

/* 订单金额 */
.order-amount {
  display: flex;
  align-items: baseline;
  justify-content: flex-end;
  gap: var(--space-2);
  padding: var(--space-4);
  border-bottom: 1rpx solid var(--border-color);
}

.amount-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.amount-value {
  font-size: var(--text-lg);
  color: var(--danger-color);
  font-weight: 700;
}

.final-payment {
  font-size: var(--text-sm);
  color: var(--text-muted);
}

/* 订单操作 */
.order-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4);
}

.order-actions .btn {
  min-width: 120rpx;
  height: 64rpx;
  font-size: var(--text-sm);
}

/* 安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
}
