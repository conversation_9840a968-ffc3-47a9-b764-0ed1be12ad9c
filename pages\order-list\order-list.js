// pages/order-list/order-list.js
import orderApi from '../../api/order.js';

Page({
  data: {
    currentTab: 'all', // all, wait_payment, paid, shipping, completed
    orders: [],
    loading: true,
    refreshing: false
  },

  onLoad() {
    wx.setNavigationBarTitle({
      title: '我的订单'
    });
    this.loadOrders();
  },

  onShow() {
    this.loadOrders();
  },

  // 加载订单列表
  async loadOrders() {
    try {
      this.setData({ loading: true });
      const app = getApp();
      const res = await orderApi.getOrderList({
        status: this.data.currentTab === 'all' ? '' : this.data.currentTab
      }, { mock: app.globalData.mockMode });
      
      if (res.code === 0) {
        this.setData({
          orders: res.data.list,
          loading: false
        });
      }
    } catch (error) {
      console.error('加载订单列表失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 切换标签
  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab;
    if (tab !== this.data.currentTab) {
      this.setData({
        currentTab: tab
      });
      this.loadOrders();
    }
  },

  // 查看订单详情
  onOrderDetail(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?id=${orderId}`
    });
  },

  // 支付订单
  async onPayOrder(e) {
    const orderId = e.currentTarget.dataset.id;
    
    try {
      wx.showLoading({
        title: '支付中...'
      });

      // 这里应该调用支付接口
      // const res = await orderApi.payOrder(orderId);
      
      wx.hideLoading();
      wx.showToast({
        title: '支付成功',
        icon: 'success'
      });
      
      setTimeout(() => {
        this.loadOrders();
      }, 1500);
    } catch (error) {
      wx.hideLoading();
      console.error('支付失败:', error);
      wx.showToast({
        title: '支付失败',
        icon: 'none'
      });
    }
  },

  // 取消订单
  async onCancelOrder(e) {
    const orderId = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个订单吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            const app = getApp();
            const result = await orderApi.cancelOrder(orderId, '用户主动取消', { mock: app.globalData.mockMode });
            if (result.code === 0) {
              wx.showToast({
                title: '订单已取消',
                icon: 'success'
              });
              this.loadOrders();
            }
          } catch (error) {
            console.error('取消订单失败:', error);
            wx.showToast({
              title: '操作失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 确认收货
  async onConfirmReceipt(e) {
    const orderId = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '确认收货',
      content: '确认已收到商品吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            const app = getApp();
            const result = await orderApi.confirmReceipt(orderId, { mock: app.globalData.mockMode });
            if (result.code === 0) {
              wx.showToast({
                title: '确认收货成功',
                icon: 'success'
              });
              this.loadOrders();
            }
          } catch (error) {
            console.error('确认收货失败:', error);
            wx.showToast({
              title: '操作失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 联系客服
  onContactService() {
    wx.showModal({
      title: '联系客服',
      content: '客服电话：************\n工作时间：9:00-18:00',
      showCancel: false
    });
  },

  // 获取订单状态文本
  getStatusText(status) {
    const statusMap = {
      'wait_payment': '待付款',
      'paid': '已付款',
      'wait_final_payment': '待付尾款',
      'shipping': '已发货',
      'completed': '已完成',
      'cancelled': '已取消'
    };
    return statusMap[status] || '未知状态';
  },

  // 获取订单状态样式
  getStatusClass(status) {
    const classMap = {
      'wait_payment': 'tag-warning',
      'paid': 'tag-info',
      'wait_final_payment': 'tag-warning',
      'shipping': 'tag-info',
      'completed': 'tag-success',
      'cancelled': 'tag-gray'
    };
    return classMap[status] || 'tag-gray';
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({ refreshing: true });
    this.loadOrders().finally(() => {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onReachBottom() {
    // 这里可以实现分页加载
    console.log('加载更多订单');
  }
});
