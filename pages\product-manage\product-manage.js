// pages/product-manage/product-manage.js
import dealerApi from '../../api/dealer.js';

Page({
  data: {
    currentTab: 'stock', // stock: 现货商品, presale: 预售商品
    products: [],
    loading: true,
    selectedProducts: []
  },

  onLoad() {
    wx.setNavigationBarTitle({
      title: '商品管理'
    });
    this.loadProducts();
  },

  // 加载商品列表
  async loadProducts() {
    try {
      this.setData({ loading: true });
      const res = await dealerApi.getProducts({ 
        type: this.data.currentTab 
      }, { mock: true });
      
      if (res.code === 0) {
        this.setData({
          products: res.data.list,
          loading: false
        });
      }
    } catch (error) {
      console.error('加载商品列表失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 切换标签
  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab;
    if (tab !== this.data.currentTab) {
      this.setData({
        currentTab: tab,
        selectedProducts: []
      });
      this.loadProducts();
    }
  },

  // 创建预售
  onCreatePresale() {
    wx.navigateTo({
      url: '/pages/create-presale/create-presale'
    });
  },

  // 开始销售
  async onStartSelling(e) {
    const productId = e.currentTarget.dataset.id;
    
    try {
      const res = await dealerApi.startSelling(productId, { mock: true });
      if (res.code === 0) {
        wx.showToast({
          title: '已开始销售',
          icon: 'success'
        });
        this.loadProducts();
      }
    } catch (error) {
      console.error('开始销售失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  // 下架商品
  async onOfflineProduct(e) {
    const productId = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '确认下架',
      content: '确定要下架这个商品吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            const result = await dealerApi.offlineProduct(productId, { mock: true });
            if (result.code === 0) {
              wx.showToast({
                title: '已下架',
                icon: 'success'
              });
              this.loadProducts();
            }
          } catch (error) {
            console.error('下架商品失败:', error);
            wx.showToast({
              title: '操作失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 编辑商品
  onEditProduct(e) {
    const productId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/edit-product/edit-product?id=${productId}`
    });
  },

  // 查看商品详情
  onProductDetail(e) {
    const productId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?id=${productId}`
    });
  },

  // 批量操作
  onBatchOperation() {
    if (this.data.selectedProducts.length === 0) {
      wx.showToast({
        title: '请选择商品',
        icon: 'none'
      });
      return;
    }

    wx.showActionSheet({
      itemList: ['批量上架', '批量下架', '批量删除'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.batchOnline();
            break;
          case 1:
            this.batchOffline();
            break;
          case 2:
            this.batchDelete();
            break;
        }
      }
    });
  },

  // 批量上架
  async batchOnline() {
    // 实现批量上架逻辑
    wx.showToast({
      title: '批量上架成功',
      icon: 'success'
    });
    this.setData({ selectedProducts: [] });
    this.loadProducts();
  },

  // 批量下架
  async batchOffline() {
    // 实现批量下架逻辑
    wx.showToast({
      title: '批量下架成功',
      icon: 'success'
    });
    this.setData({ selectedProducts: [] });
    this.loadProducts();
  },

  // 批量删除
  async batchDelete() {
    wx.showModal({
      title: '确认删除',
      content: `确定要删除选中的${this.data.selectedProducts.length}个商品吗？`,
      success: async (res) => {
        if (res.confirm) {
          // 实现批量删除逻辑
          wx.showToast({
            title: '批量删除成功',
            icon: 'success'
          });
          this.setData({ selectedProducts: [] });
          this.loadProducts();
        }
      }
    });
  },

  // 选择商品
  onSelectProduct(e) {
    const productId = e.currentTarget.dataset.id;
    const selectedProducts = [...this.data.selectedProducts];
    const index = selectedProducts.indexOf(productId);
    
    if (index > -1) {
      selectedProducts.splice(index, 1);
    } else {
      selectedProducts.push(productId);
    }
    
    this.setData({ selectedProducts });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadProducts().finally(() => {
      wx.stopPullDownRefresh();
    });
  }
});
