/* pages/index/index.wxss */
.page {
  background: linear-gradient(180deg, var(--background) 0%, #ffffff 100%);
  min-height: 100vh;
  padding-bottom: 160rpx;
}

/* Hero Section */
.hero-section {
  margin-bottom: var(--space-6);
  border-radius: 0 0 var(--radius-xl) var(--radius-xl);
  overflow: hidden;
}

.hero-swiper {
  width: 100%;
  height: 320rpx;
}

.hero-slide {
  position: relative;
  width: 100%;
  height: 100%;
}

.hero-image {
  width: 100%;
  height: 100%;
}

.hero-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: var(--space-8) var(--space-4) var(--space-6);
}

.hero-content {
  color: var(--text-white);
}

.hero-title {
  display: block;
  font-size: var(--text-2xl);
  font-weight: 700;
  margin-bottom: var(--space-2);
}

.hero-subtitle {
  display: block;
  font-size: var(--text-base);
  opacity: 0.9;
}

/* Quick Info Section */
.quick-info {
  padding: 0 var(--space-4);
  margin-bottom: var(--space-8);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-3);
  margin-bottom: var(--space-5);
}

.info-item {
  background: var(--surface);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  text-align: center;
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-color);
}

.info-icon-wrapper {
  width: 64rpx;
  height: 64rpx;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-3);
}

.info-icon-wrapper.dealer {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.info-icon-wrapper.school {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.info-icon-wrapper.referrer {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.info-icon {
  font-size: 36rpx;
}

.info-text {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.info-label {
  font-size: var(--text-xs);
  color: var(--text-muted);
  margin-bottom: var(--space-1);
}

.info-value {
  font-size: var(--text-sm);
  color: var(--text-primary);
  font-weight: 500;
  text-align: center;
  line-height: 1.3;
}

/* Promo Card */
.promo-card {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-radius: var(--radius-lg);
  padding: var(--space-5);
  color: var(--text-white);
  box-shadow: var(--shadow-lg);
}

.promo-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.promo-left {
  flex: 1;
}

.promo-title {
  display: block;
  font-size: var(--text-sm);
  opacity: 0.9;
  margin-bottom: var(--space-2);
}

.promo-code {
  display: block;
  font-size: var(--text-xl);
  font-weight: 700;
  letter-spacing: 2rpx;
}

.promo-right {
  margin-left: var(--space-4);
}

.promo-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 500;
}

/* Products Section */
.products-section {
  padding: 0 var(--space-4);
  margin-bottom: var(--space-8);
}

.section-header {
  text-align: center;
  margin-bottom: var(--space-6);
}

.section-title {
  display: block;
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.section-subtitle {
  display: block;
  font-size: var(--text-base);
  color: var(--text-secondary);
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-12) 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--gray-200);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: var(--text-muted);
  font-size: var(--text-sm);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-4);
}

.product-card {
  background: var(--surface);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-color);
}

.product-image-wrapper {
  position: relative;
  width: 100%;
  height: 240rpx;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.product-card:active .product-image {
  transform: scale(1.05);
}

.product-status-badge {
  position: absolute;
  top: var(--space-3);
  left: var(--space-3);
}

.product-content {
  padding: var(--space-4);
}

.product-name {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-3);
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-price-section {
  margin-bottom: var(--space-3);
}

.price-group {
  display: flex;
  align-items: baseline;
  gap: var(--space-2);
}

.current-price {
  font-size: var(--text-lg);
  font-weight: 700;
  color: var(--danger-color);
}

.original-price {
  font-size: var(--text-sm);
  color: var(--text-muted);
  text-decoration: line-through;
}

.deposit-price {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--warning-color);
  display: block;
  margin-bottom: var(--space-1);
}

.final-price {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.product-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
}

.sales-count {
  font-size: var(--text-xs);
  color: var(--text-muted);
}

.presale-progress {
  font-size: var(--text-xs);
  color: var(--warning-color);
  font-weight: 500;
}

.progress-text::before {
  content: '进度: ';
}

.product-actions {
  padding: 0 var(--space-4) var(--space-4);
}

/* Floating Cart */
.floating-cart {
  position: fixed;
  bottom: var(--space-8);
  right: var(--space-4);
  z-index: 1000;
}

.cart-button {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-xl);
  transition: all 0.3s ease;
}

.cart-button:active {
  transform: scale(0.95);
  box-shadow: var(--shadow-lg);
}

.cart-icon {
  font-size: 48rpx;
  color: var(--text-white);
}

.cart-count {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 40rpx;
  height: 40rpx;
  background: var(--danger-color);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-xs);
  font-weight: 600;
  color: var(--text-white);
  border: 4rpx solid var(--surface);
  box-shadow: var(--shadow-md);
}

/* Safe Area */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
}
