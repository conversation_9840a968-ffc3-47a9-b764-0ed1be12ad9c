// pages/order-submit/order-submit.js
import orderApi from '../../api/order.js';
import addressApi from '../../api/address.js';
import productApi from '../../api/product.js';

Page({
  data: {
    orderType: 'cart', // cart: 购物车结算, buy_now: 立即购买
    products: [],
    address: null,
    paymentMethod: 'wechat', // wechat: 微信支付, alipay: 支付宝
    totalAmount: 0,
    agreementChecked: true,
    hasPresaleProducts: false,
    loading: true
  },

  onLoad(options) {
    wx.setNavigationBarTitle({
      title: '订单提交'
    });
    
    this.setData({
      orderType: options.type || 'cart'
    });

    if (options.type === 'buy_now') {
      // 立即购买
      this.loadBuyNowProduct(options);
    } else {
      // 购物车结算
      this.loadCartProducts();
    }
    
    this.loadDefaultAddress();
  },

  // 加载立即购买商品
  async loadBuyNowProduct(options) {
    try {
      const res = await productApi.getProductDetail(options.productId, { mock: true });
      if (res.code === 0) {
        const product = res.data;
        const products = [{
          id: product.id,
          name: product.name,
          image: product.images[0],
          price: product.status === 'in_stock' ? product.price : product.deposit,
          size: options.size,
          quantity: parseInt(options.quantity),
          isPreSale: product.status === 'pre_sale'
        }];
        
        this.setData({
          products: products,
          hasPresaleProducts: products.some(p => p.isPreSale),
          loading: false
        });
        this.calculateTotal();
      }
    } catch (error) {
      console.error('加载商品信息失败:', error);
      this.setData({ loading: false });
    }
  },

  // 加载购物车商品
  async loadCartProducts() {
    try {
      const res = await productApi.getCartList({ mock: true });
      if (res.code === 0) {
        const selectedProducts = res.data.list.filter(item => item.selected);
        this.setData({
          products: selectedProducts,
          hasPresaleProducts: selectedProducts.some(p => p.isPreSale),
          loading: false
        });
        this.calculateTotal();
      }
    } catch (error) {
      console.error('加载购物车失败:', error);
      this.setData({ loading: false });
    }
  },

  // 加载默认地址
  async loadDefaultAddress() {
    try {
      const res = await addressApi.getAddressList({ mock: true });
      if (res.code === 0) {
        const defaultAddress = res.data.list.find(addr => addr.isDefault);
        this.setData({
          address: defaultAddress || res.data.list[0]
        });
      }
    } catch (error) {
      console.error('加载地址失败:', error);
    }
  },

  // 计算总金额
  calculateTotal() {
    const total = this.data.products.reduce((sum, product) => {
      return sum + (product.price * product.quantity);
    }, 0);
    
    this.setData({
      totalAmount: total
    });
  },

  // 选择地址
  onSelectAddress() {
    wx.navigateTo({
      url: '/pages/address-list/address-list?from=order'
    });
  },

  // 选择支付方式
  onPaymentChange(e) {
    this.setData({
      paymentMethod: e.detail.value
    });
  },

  // 协议勾选
  onAgreementChange(e) {
    this.setData({
      agreementChecked: e.detail.value
    });
  },

  // 查看协议
  onViewAgreement() {
    wx.navigateTo({
      url: '/pages/agreement/agreement'
    });
  },

  // 提交订单
  async onSubmitOrder() {
    if (!this.validateOrder()) return;

    try {
      wx.showLoading({
        title: '提交中...'
      });

      const orderData = {
        products: this.data.products.map(p => ({
          productId: p.id,
          size: p.size,
          quantity: p.quantity
        })),
        addressId: this.data.address.id,
        paymentMethod: this.data.paymentMethod,
        totalAmount: this.data.totalAmount
      };

      const res = await orderApi.submitOrder(orderData, { mock: true });
      
      if (res.code === 0) {
        wx.hideLoading();
        wx.showToast({
          title: '订单提交成功',
          icon: 'success'
        });
        
        // 跳转到订单列表页
        setTimeout(() => {
          wx.redirectTo({
            url: '/pages/order-list/order-list'
          });
        }, 1500);
      }
    } catch (error) {
      wx.hideLoading();
      console.error('提交订单失败:', error);
      wx.showToast({
        title: '提交失败',
        icon: 'none'
      });
    }
  },

  // 验证订单
  validateOrder() {
    if (!this.data.address) {
      wx.showToast({
        title: '请选择收货地址',
        icon: 'none'
      });
      return false;
    }

    if (this.data.products.length === 0) {
      wx.showToast({
        title: '购物车为空',
        icon: 'none'
      });
      return false;
    }

    if (!this.data.agreementChecked) {
      wx.showToast({
        title: '请同意相关协议',
        icon: 'none'
      });
      return false;
    }

    return true;
  }
});
