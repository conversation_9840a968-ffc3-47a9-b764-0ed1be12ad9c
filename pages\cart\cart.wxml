<!-- pages/cart/cart.wxml -->
<view class="page">
  <!-- 头部操作栏 -->
  <view class="header-bar">
    <view class="header-left">
      <text class="cart-title">购物车</text>
      <text class="cart-count">（{{cartItems.length}}件商品）</text>
    </view>
    <view class="header-right">
      <text class="edit-btn" bindtap="onToggleEditMode">{{editMode ? '完成' : '编辑'}}</text>
    </view>
  </view>

  <!-- 购物车内容 -->
  <view class="cart-content">
    <view wx:if="{{loading}}" class="loading-state">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <view wx:elif="{{cartItems.length === 0}}" class="empty-state">
      <text class="empty-icon">🛒</text>
      <text class="empty-text">购物车是空的</text>
      <text class="empty-hint">快去选购心仪的商品吧</text>
      <button class="btn btn-primary continue-btn" bindtap="onContinueShopping">
        继续购物
      </button>
    </view>

    <view wx:else class="cart-list">
      <view class="cart-item" wx:for="{{cartItems}}" wx:key="id">
        <!-- 选择框 -->
        <view class="item-checkbox" bindtap="onSelectItem" data-id="{{item.id}}">
          <view class="checkbox {{selectedItems.indexOf(item.id) !== -1 ? 'checked' : ''}}">
            <text wx:if="{{selectedItems.indexOf(item.id) !== -1}}" class="check-icon">✓</text>
          </view>
        </view>

        <!-- 商品信息 -->
        <view class="item-content">
          <image class="item-image" src="{{item.image}}" mode="aspectFill"></image>
          
          <view class="item-details">
            <text class="item-name">{{item.name}}</text>
            <view class="item-specs">
              <text class="spec-text">{{item.size}}</text>
              <view wx:if="{{item.isPreSale}}" class="presale-tag">
                <text class="tag-text">预售</text>
              </view>
            </view>
            
            <view class="item-price-section">
              <view class="price-info">
                <text wx:if="{{item.isPreSale}}" class="price-label">定金</text>
                <text class="item-price">¥{{item.price}}</text>
              </view>
              
              <!-- 数量控制 -->
              <view class="quantity-controls">
                <button class="quantity-btn" bindtap="onQuantityChange" data-id="{{item.id}}" data-type="minus" disabled="{{item.quantity <= 1}}">
                  <text class="btn-icon">−</text>
                </button>
                <text class="quantity-number">{{item.quantity}}</text>
                <button class="quantity-btn" bindtap="onQuantityChange" data-id="{{item.id}}" data-type="plus">
                  <text class="btn-icon">+</text>
                </button>
              </view>
            </view>
          </view>

          <!-- 删除按钮 -->
          <view wx:if="{{editMode}}" class="item-delete" bindtap="onDeleteItem" data-id="{{item.id}}">
            <text class="delete-icon">🗑️</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-bar" wx:if="{{cartItems.length > 0}}">
    <view class="bar-content">
      <view class="bar-left">
        <view class="select-all" bindtap="onSelectAll">
          <view class="checkbox {{allSelected ? 'checked' : ''}}">
            <text wx:if="{{allSelected}}" class="check-icon">✓</text>
          </view>
          <text class="select-text">全选</text>
        </view>

        <view class="total-info" wx:if="{{!editMode}}">
          <text class="total-label">合计：</text>
          <text class="total-amount">¥{{totalAmount}}</text>
        </view>
      </view>

      <view class="bar-right">
        <button wx:if="{{editMode}}" class="btn btn-danger delete-btn" bindtap="onBatchDelete">
          删除选中
        </button>
        <button wx:else class="btn btn-primary checkout-btn" bindtap="onCheckout" disabled="{{selectedItems.length === 0}}">
          去结算（{{selectedItems.length}}）
        </button>
      </view>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>
