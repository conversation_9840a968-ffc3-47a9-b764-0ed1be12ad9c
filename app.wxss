/* app.wxss */
/* 现代化设计系统 */
page {
  /* 主色调 - 优雅的蓝紫色系 */
  --primary-color: #6366f1;      /* 主色 - 现代紫蓝 */
  --primary-light: #a5b4fc;      /* 主色浅色 */
  --primary-dark: #4338ca;       /* 主色深色 */

  /* 辅助色彩 */
  --success-color: #10b981;      /* 成功绿 */
  --warning-color: #f59e0b;      /* 警告橙 */
  --danger-color: #ef4444;       /* 危险红 */
  --info-color: #3b82f6;         /* 信息蓝 */

  /* 中性色系 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* 文本颜色 */
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-600);
  --text-muted: var(--gray-400);
  --text-white: #ffffff;

  /* 背景色 */
  --background: var(--gray-50);
  --surface: #ffffff;
  --surface-hover: var(--gray-100);

  /* 边框和分割线 */
  --border-color: var(--gray-200);
  --divider-color: var(--gray-100);

  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* 字体大小 */
  --text-xs: 22rpx;
  --text-sm: 26rpx;
  --text-base: 28rpx;
  --text-lg: 32rpx;
  --text-xl: 36rpx;
  --text-2xl: 42rpx;
  --text-3xl: 52rpx;

  /* 间距系统 */
  --space-1: 6rpx;
  --space-2: 12rpx;
  --space-3: 18rpx;
  --space-4: 24rpx;
  --space-5: 30rpx;
  --space-6: 36rpx;
  --space-8: 48rpx;
  --space-10: 60rpx;
  --space-12: 72rpx;
  --space-16: 96rpx;

  /* 圆角 */
  --radius-sm: 8rpx;
  --radius-md: 12rpx;
  --radius-lg: 16rpx;
  --radius-xl: 24rpx;
  --radius-full: 9999rpx;

  background-color: var(--background);
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: var(--text-primary);
}

/* 布局组件 */
.container {
  padding: 0 var(--space-4);
  box-sizing: border-box;
}

.section {
  margin-bottom: var(--space-6);
}

/* 卡片系统 */
.card {
  background-color: var(--surface);
  border-radius: var(--radius-lg);
  padding: var(--space-5);
  margin-bottom: var(--space-4);
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-color);
}

.card-elevated {
  box-shadow: var(--shadow-md);
  border: none;
}

.card-hover {
  transition: all 0.2s ease;
}

.card-hover:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-sm);
}

/* 按钮系统 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  font-weight: 500;
  transition: all 0.2s ease;
  border: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.btn:active::before {
  opacity: 1;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--text-white);
  height: 76rpx;
  min-width: 180rpx;
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--surface);
  color: var(--text-primary);
  border: 2rpx solid var(--border-color);
  height: 68rpx;
}

.btn-ghost {
  background-color: transparent;
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
  height: 68rpx;
}

.btn-success {
  background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
  color: var(--text-white);
  height: 76rpx;
}

.btn-warning {
  background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
  color: var(--text-white);
  height: 76rpx;
}

.btn-danger {
  background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%);
  color: var(--text-white);
  height: 76rpx;
}

.btn-sm {
  height: 56rpx;
  font-size: var(--text-sm);
  padding: 0 var(--space-4);
}

.btn-lg {
  height: 84rpx;
  font-size: var(--text-lg);
  padding: 0 var(--space-8);
}

/* 标签系统 */
.tag {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 500;
  line-height: 1;
}

.tag-primary {
  background-color: rgba(99, 102, 241, 0.1);
  color: var(--primary-color);
}

.tag-success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.tag-warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.tag-danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.tag-info {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

.tag-gray {
  background-color: var(--gray-100);
  color: var(--gray-600);
}

/* 状态标签（兼容旧版本） */
.status-tag {
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 500;
}

.status-success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.status-warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.status-info {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

.status-danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.status-stock {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.status-presale {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.status-pending {
  background-color: var(--gray-100);
  color: var(--gray-600);
}

/* 文本系统 */
.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }
.text-3xl { font-size: var(--text-3xl); }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-white { color: var(--text-white); }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.leading-tight { line-height: 1.25; }
.leading-normal { line-height: 1.5; }
.leading-relaxed { line-height: 1.75; }

/* 间距工具类 */
.m-0 { margin: 0; }
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-5 { margin: var(--space-5); }
.m-6 { margin: var(--space-6); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--space-1); }
.mt-2 { margin-top: var(--space-2); }
.mt-3 { margin-top: var(--space-3); }
.mt-4 { margin-top: var(--space-4); }
.mt-5 { margin-top: var(--space-5); }
.mt-6 { margin-top: var(--space-6); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-5 { margin-bottom: var(--space-5); }
.mb-6 { margin-bottom: var(--space-6); }

.p-0 { padding: 0; }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-5 { padding: var(--space-5); }
.p-6 { padding: var(--space-6); }

.px-0 { padding-left: 0; padding-right: 0; }
.px-1 { padding-left: var(--space-1); padding-right: var(--space-1); }
.px-2 { padding-left: var(--space-2); padding-right: var(--space-2); }
.px-3 { padding-left: var(--space-3); padding-right: var(--space-3); }
.px-4 { padding-left: var(--space-4); padding-right: var(--space-4); }

.py-0 { padding-top: 0; padding-bottom: 0; }
.py-1 { padding-top: var(--space-1); padding-bottom: var(--space-1); }
.py-2 { padding-top: var(--space-2); padding-bottom: var(--space-2); }
.py-3 { padding-top: var(--space-3); padding-bottom: var(--space-3); }
.py-4 { padding-top: var(--space-4); padding-bottom: var(--space-4); }

/* 布局工具类 */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

.flex-1 { flex: 1; }
.flex-none { flex: none; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }

.overflow-hidden { overflow: hidden; }

/* 动画 */
.transition { transition: all 0.2s ease; }
.transition-fast { transition: all 0.1s ease; }
.transition-slow { transition: all 0.3s ease; }
