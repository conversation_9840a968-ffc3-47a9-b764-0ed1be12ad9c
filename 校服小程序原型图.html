<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>校服销售小程序原型图</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .prototype-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .page-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .page-mockup {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: #fafafa;
            min-height: 400px;
        }
        .page-title {
            background: #007bff;
            color: white;
            padding: 8px 12px;
            margin: -15px -15px 15px -15px;
            border-radius: 6px 6px 0 0;
            font-weight: bold;
        }
        .component {
            background: white;
            border: 1px solid #ccc;
            margin: 8px 0;
            padding: 10px;
            border-radius: 4px;
            position: relative;
        }
        .banner {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            text-align: center;
            height: 80px;
            line-height: 80px;
        }
        .info-card {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .product-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .product-image {
            width: 60px;
            height: 60px;
            background: #ddd;
            border-radius: 4px;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-warning {
            background: #ffc107;
            color: #333;
        }
        .btn-danger {
            background: #dc3545;
        }
        .form-group {
            margin: 10px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-control {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .size-selector {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }
        .size-option {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
        }
        .size-option.selected {
            background: #007bff;
            color: white;
        }
        .status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            color: white;
        }
        .status-pending {
            background: #ffc107;
            color: #333;
        }
        .status-success {
            background: #28a745;
        }
        .status-info {
            background: #17a2b8;
        }
        .tab-container {
            border-bottom: 1px solid #ddd;
            margin-bottom: 15px;
        }
        .tab {
            display: inline-block;
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
        }
        .tab.active {
            border-bottom-color: #007bff;
            color: #007bff;
        }
        .data-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            margin: 10px 0;
        }
        .quick-action {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin: 15px 0;
        }
        .action-item {
            background: #f8f9fa;
            padding: 15px;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
            border: 1px solid #dee2e6;
        }
        .action-item:hover {
            background: #e9ecef;
        }
    </style>
</head>
<body>
    <div class="prototype-container">
        <h1 style="text-align: center; color: #333;">校服销售小程序原型图</h1>
        
        <!-- 学生/家长端 -->
        <div class="section">
            <h2>一、学生/家长端（小程序）</h2>
            <div class="page-grid">
                <!-- 首页 -->
                <div class="page-mockup">
                    <div class="page-title">首页</div>
                    <div class="component banner">
                        Banner轮播图
                    </div>
                    <div class="component info-card">
                        <strong>经销商信息</strong><br>
                        📍 XX校服专营店<br>
                        📞 联系电话：138****8888
                    </div>
                    <div class="component info-card">
                        <strong>学校信息</strong><br>
                        🏫 XX中学<br>
                        📅 2024年春季校服
                    </div>
                    <div class="component info-card">
                        <strong>分销商信息</strong><br>
                        👤 推荐人：张老师<br>
                        🎯 专属优惠码：ABC123
                    </div>
                    <div class="component">
                        <strong>商品列表</strong>
                        <div class="product-item">
                            <div class="product-image"></div>
                            <div>
                                <div>春季校服套装</div>
                                <div style="color: #ff4757;">¥128.00</div>
                                <span class="status-badge status-info">现货</span>
                            </div>
                            <button class="btn">加购物车</button>
                        </div>
                        <div class="product-item">
                            <div class="product-image"></div>
                            <div>
                                <div>夏季运动服</div>
                                <div style="color: #ff4757;">定金¥20（尾款¥80）</div>
                                <span class="status-badge status-warning">预售</span>
                            </div>
                            <button class="btn">加购物车</button>
                        </div>
                    </div>
                    <div style="position: fixed; bottom: 10px; right: 10px;">
                        <button class="btn">🛒 购物车(2)</button>
                    </div>
                </div>

                <!-- 商品详情页 -->
                <div class="page-mockup">
                    <div class="page-title">商品详情页</div>
                    <div class="component">
                        <div style="height: 200px; background: #ddd; border-radius: 4px; margin-bottom: 10px;">
                            商品图片轮播
                        </div>
                        <h3>春季校服套装</h3>
                        <div style="color: #ff4757; font-size: 18px; font-weight: bold;">¥128.00</div>
                        <span class="status-badge status-success">现货充足</span>
                    </div>
                    <div class="component">
                        <div class="form-group">
                            <label>选择尺码：</label>
                            <div class="size-selector">
                                <div class="size-option">S (库存:10)</div>
                                <div class="size-option selected">M (库存:15)</div>
                                <div class="size-option">L (库存:8)</div>
                                <div class="size-option">XL (库存:5)</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>数量：</label>
                            <input type="number" class="form-control" value="1" min="1">
                        </div>
                    </div>
                    <div class="component">
                        <h4>商品详情</h4>
                        <p>优质面料，舒适透气，适合春季穿着...</p>
                    </div>
                    <div style="position: fixed; bottom: 0; left: 0; right: 0; background: white; padding: 10px; border-top: 1px solid #ddd;">
                        <button class="btn" style="width: 48%;">加入购物车</button>
                        <button class="btn btn-danger" style="width: 48%; float: right;">立即购买</button>
                    </div>
                </div>

                <!-- 订单提交页 -->
                <div class="page-mockup">
                    <div class="page-title">订单提交页</div>
                    <div class="component">
                        <strong>收货地址</strong><br>
                        张三 138****8888<br>
                        广东省深圳市南山区科技园...
                        <button class="btn" style="float: right;">更换</button>
                    </div>
                    <div class="component">
                        <strong>商品信息</strong>
                        <div class="product-item">
                            <div class="product-image"></div>
                            <div>
                                <div>夏季运动服 M码</div>
                                <div style="color: #ff4757;">定金¥20</div>
                                <div style="font-size: 12px; color: #666;">数量: 1</div>
                            </div>
                        </div>
                    </div>
                    <div class="component">
                        <strong>支付方式</strong><br>
                        <input type="radio" checked> 微信支付<br>
                        <input type="radio"> 支付宝
                    </div>
                    <div class="component" style="background: #fff3cd; border-color: #ffeaa7;">
                        <input type="checkbox" checked> 我已阅读并同意《预售协议》
                    </div>
                    <div style="position: fixed; bottom: 0; left: 0; right: 0; background: white; padding: 10px; border-top: 1px solid #ddd;">
                        <div style="float: left; line-height: 40px;">
                            合计: <span style="color: #ff4757; font-size: 18px; font-weight: bold;">¥20.00</span>
                        </div>
                        <button class="btn btn-danger" style="float: right;">提交订单</button>
                    </div>
                </div>

                <!-- 订单详情页 -->
                <div class="page-mockup">
                    <div class="page-title">订单详情页</div>
                    <div class="component">
                        <strong>订单状态</strong>
                        <span class="status-badge status-info">待付尾款</span><br>
                        <small>订单号: 202401150001</small>
                    </div>
                    <div class="component">
                        <strong>商品信息</strong>
                        <div class="product-item">
                            <div class="product-image"></div>
                            <div>
                                <div>夏季运动服 M码</div>
                                <div>定金¥20 + 尾款¥80</div>
                            </div>
                        </div>
                    </div>
                    <div class="component">
                        <strong>生产进度</strong><br>
                        ✅ 订单确认 (2024-01-15)<br>
                        ✅ 开始生产 (2024-01-20)<br>
                        🔄 生产中 (预计2024-02-10完成)<br>
                        ⏳ 待发货<br>
                        ⏳ 已发货
                    </div>
                    <div class="component">
                        <strong>收货地址</strong><br>
                        张三 138****8888<br>
                        广东省深圳市南山区科技园...
                    </div>
                    <button class="btn btn-warning" style="width: 100%;">支付尾款 ¥80</button>
                </div>

                <!-- 个人中心 -->
                <div class="page-mockup">
                    <div class="page-title">个人中心</div>
                    <div class="component">
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <div style="width: 60px; height: 60px; background: #ddd; border-radius: 50%;"></div>
                            <div>
                                <div><strong>张三</strong></div>
                                <div style="color: #666;">手机号: 138****8888</div>
                            </div>
                        </div>
                    </div>
                    <div class="component">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>🏪 切换经销商身份</span>
                            <span style="color: #666;">待审核 ></span>
                        </div>
                    </div>
                    <div class="component">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>📋 我的订单</span>
                            <span style="color: #666;">></span>
                        </div>
                    </div>
                    <div class="component">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>🎯 我的推广码</span>
                            <span style="color: #666;">ABC123 ></span>
                        </div>
                    </div>
                    <div class="component">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>📊 推广业绩</span>
                            <span style="color: #666;">></span>
                        </div>
                    </div>
                    <div class="component">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span>⚙️ 设置</span>
                            <span style="color: #666;">></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 经销商端 -->
        <div class="section">
            <h2>二、经销商端（小程序）</h2>
            <div class="page-grid">
                <!-- 工作台 -->
                <div class="page-mockup">
                    <div class="page-title">工作台</div>
                    <div class="component" style="background: #fff3cd; border-color: #ffeaa7;">
                        <strong>⚠️ 资质认证</strong><br>
                        请完成营业执照和学校授权书上传
                        <button class="btn btn-warning" style="float: right;">去认证</button>
                    </div>
                    <div class="data-card">
                        <h3>今日数据</h3>
                        <div style="display: flex; justify-content: space-around;">
                            <div>
                                <div style="font-size: 24px; font-weight: bold;">15</div>
                                <div>新订单</div>
                            </div>
                            <div>
                                <div style="font-size: 24px; font-weight: bold;">¥3,200</div>
                                <div>销售额</div>
                            </div>
                            <div>
                                <div style="font-size: 24px; font-weight: bold;">85%</div>
                                <div>预售达成率</div>
                            </div>
                        </div>
                    </div>
                    <div class="component">
                        <strong>快捷入口</strong>
                        <div class="quick-action">
                            <div class="action-item">
                                <div>📦</div>
                                <div>商品管理</div>
                            </div>
                            <div class="action-item">
                                <div>📋</div>
                                <div>订单处理</div>
                            </div>
                            <div class="action-item">
                                <div>👥</div>
                                <div>分销管理</div>
                            </div>
                        </div>
                    </div>
                    <div class="component">
                        <strong>待处理事项</strong><br>
                        • 5个预售订单待导出生产<br>
                        • 3个现货订单待发货<br>
                        • 2个分销员申请待审核
                    </div>
                </div>

                <!-- 商品管理 -->
                <div class="page-mockup">
                    <div class="page-title">商品管理</div>
                    <div class="tab-container">
                        <div class="tab active">现货商品</div>
                        <div class="tab">预售商品</div>
                    </div>
                    <div class="component">
                        <button class="btn btn-success">+ 创建预售</button>
                        <button class="btn">批量操作</button>
                    </div>
                    <div class="component">
                        <div class="product-item">
                            <div class="product-image"></div>
                            <div style="flex: 1;">
                                <div><strong>春季校服套装</strong></div>
                                <div>现货 | ¥128.00 | 库存:50</div>
                                <span class="status-badge status-success">销售中</span>
                            </div>
                            <div>
                                <button class="btn">编辑</button>
                                <button class="btn btn-warning">下架</button>
                            </div>
                        </div>
                        <div class="product-item">
                            <div class="product-image"></div>
                            <div style="flex: 1;">
                                <div><strong>夏季运动服</strong></div>
                                <div>预售 | 定金¥20 | 已售:25/100</div>
                                <span class="status-badge status-pending">待开始</span>
                            </div>
                            <div>
                                <button class="btn btn-success">开始销售</button>
                                <button class="btn">编辑</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 订单处理 -->
                <div class="page-mockup">
                    <div class="page-title">订单处理</div>
                    <div class="tab-container">
                        <div class="tab active">预售订单</div>
                        <div class="tab">现货订单</div>
                    </div>
                    <div class="component">
                        <button class="btn btn-success">导出生产订单</button>
                        <button class="btn">批量发货</button>
                    </div>
                    <div class="component">
                        <div style="border-bottom: 1px solid #eee; padding: 10px 0;">
                            <div style="display: flex; justify-content: space-between;">
                                <div>
                                    <strong>订单号: 202401150001</strong><br>
                                    <small>夏季运动服 M码 x1</small>
                                </div>
                                <div style="text-align: right;">
                                    <div>定金¥20</div>
                                    <span class="status-badge status-info">已付定金</span>
                                </div>
                            </div>
                            <div style="margin-top: 10px;">
                                <button class="btn">更新进度</button>
                                <button class="btn btn-warning">联系客户</button>
                            </div>
                        </div>
                        <div style="border-bottom: 1px solid #eee; padding: 10px 0;">
                            <div style="display: flex; justify-content: space-between;">
                                <div>
                                    <strong>订单号: 202401150002</strong><br>
                                    <small>春季校服套装 L码 x2</small>
                                </div>
                                <div style="text-align: right;">
                                    <div>¥256</div>
                                    <span class="status-badge status-success">已发货</span>
                                </div>
                            </div>
                            <div style="margin-top: 10px;">
                                <button class="btn">查看物流</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分销管理 -->
                <div class="page-mockup">
                    <div class="page-title">分销管理</div>
                    <div class="data-card">
                        <h4>分销数据</h4>
                        <div style="display: flex; justify-content: space-around;">
                            <div>
                                <div style="font-size: 20px; font-weight: bold;">12</div>
                                <div>分销员</div>
                            </div>
                            <div>
                                <div style="font-size: 20px; font-weight: bold;">¥1,280</div>
                                <div>分销佣金</div>
                            </div>
                            <div>
                                <div style="font-size: 20px; font-weight: bold;">68</div>
                                <div>推广订单</div>
                            </div>
                        </div>
                    </div>
                    <div class="component">
                        <strong>佣金设置</strong><br>
                        <div class="form-group">
                            <label>一级分销员佣金比例:</label>
                            <input type="number" class="form-control" value="8" min="5" max="15"> %
                        </div>
                        <div class="form-group">
                            <label>二级分销员佣金比例:</label>
                            <input type="number" class="form-control" value="3" min="1" max="8"> %
                        </div>
                        <button class="btn btn-success">保存设置</button>
                    </div>
                    <div class="component">
                        <strong>分销员列表</strong>
                        <div style="border-bottom: 1px solid #eee; padding: 10px 0;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <strong>张老师</strong><br>
                                    <small>推广码: ABC123 | 订单: 15</small>
                                </div>
                                <div style="text-align: right;">
                                    <div>佣金: ¥320</div>
                                    <span class="status-badge status-success">已审核</span>
                                </div>
                            </div>
                        </div>
                        <div style="border-bottom: 1px solid #eee; padding: 10px 0;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <strong>李家长</strong><br>
                                    <small>推广码: DEF456 | 订单: 8</small>
                                </div>
                                <div style="text-align: right;">
                                    <div>佣金: ¥160</div>
                                    <span class="status-badge status-pending">待审核</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 平台管理后台 -->
        <div class="section">
            <h2>三、平台管理后台（PC端）</h2>
            <div class="page-grid">
                <!-- 经销商审核 -->
                <div class="page-mockup">
                    <div class="page-title">经销商审核</div>
                    <div class="component">
                        <strong>待审核列表</strong>
                        <div style="border: 1px solid #ddd; border-radius: 4px; padding: 15px; margin: 10px 0;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <strong>XX校服专营店</strong><br>
                                    <small>申请人: 王经理 | 手机: 138****8888</small><br>
                                    <small>申请时间: 2024-01-15 10:30</small>
                                </div>
                                <div>
                                    <button class="btn btn-success">审核通过</button>
                                    <button class="btn btn-danger">驳回</button>
                                </div>
                            </div>
                            <div style="margin-top: 15px;">
                                <strong>资质文件:</strong><br>
                                📄 营业执照.pdf &nbsp;&nbsp;
                                📄 学校授权书.pdf &nbsp;&nbsp;
                                <button class="btn">预览</button>
                            </div>
                        </div>
                    </div>
                    <div class="component">
                        <strong>驳回原因</strong>
                        <textarea class="form-control" rows="3" placeholder="请填写驳回原因..."></textarea>
                        <button class="btn btn-danger" style="margin-top: 10px;">确认驳回</button>
                    </div>
                </div>

                <!-- 经销商列表 -->
                <div class="page-mockup">
                    <div class="page-title">经销商列表</div>
                    <div class="component">
                        <div class="form-group">
                            <input type="text" class="form-control" placeholder="搜索经销商名称...">
                        </div>
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr style="background: #f8f9fa;">
                                    <th style="padding: 10px; border: 1px solid #ddd;">经销商名称</th>
                                    <th style="padding: 10px; border: 1px solid #ddd;">联系人</th>
                                    <th style="padding: 10px; border: 1px solid #ddd;">状态</th>
                                    <th style="padding: 10px; border: 1px solid #ddd;">销售额</th>
                                    <th style="padding: 10px; border: 1px solid #ddd;">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td style="padding: 10px; border: 1px solid #ddd;">XX校服专营店</td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">王经理</td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">
                                        <span class="status-badge status-success">正常</span>
                                    </td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">¥25,680</td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">
                                        <button class="btn">查看</button>
                                        <button class="btn btn-warning">暂停</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="padding: 10px; border: 1px solid #ddd;">YY学生用品店</td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">李老板</td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">
                                        <span class="status-badge status-pending">待审核</span>
                                    </td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">¥0</td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">
                                        <button class="btn">审核</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 佣金配置 -->
                <div class="page-mockup">
                    <div class="page-title">佣金配置</div>
                    <div class="component">
                        <strong>平台基础佣金设置</strong>
                        <div class="form-group">
                            <label>平台抽成比例:</label>
                            <input type="number" class="form-control" value="5" min="1" max="20"> %
                        </div>
                        <div class="form-group">
                            <label>经销商基础佣金:</label>
                            <input type="number" class="form-control" value="10" min="5" max="30"> %
                        </div>
                    </div>
                    <div class="component">
                        <strong>经销商等级配置</strong>
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr style="background: #f8f9fa;">
                                    <th style="padding: 10px; border: 1px solid #ddd;">等级</th>
                                    <th style="padding: 10px; border: 1px solid #ddd;">销售额要求</th>
                                    <th style="padding: 10px; border: 1px solid #ddd;">佣金比例</th>
                                    <th style="padding: 10px; border: 1px solid #ddd;">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td style="padding: 10px; border: 1px solid #ddd;">普通</td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">¥0</td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">8%</td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">
                                        <button class="btn">编辑</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="padding: 10px; border: 1px solid #ddd;">银牌</td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">¥10,000</td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">10%</td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">
                                        <button class="btn">编辑</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="padding: 10px; border: 1px solid #ddd;">金牌</td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">¥50,000</td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">12%</td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">
                                        <button class="btn">编辑</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <button class="btn btn-success" style="margin-top: 15px;">保存配置</button>
                    </div>
                </div>

                <!-- 数据报表 -->
                <div class="page-mockup">
                    <div class="page-title">数据报表</div>
                    <div class="component">
                        <strong>筛选条件</strong>
                        <div style="display: flex; gap: 10px; margin: 10px 0;">
                            <input type="date" class="form-control" style="width: auto;">
                            <span style="line-height: 38px;">至</span>
                            <input type="date" class="form-control" style="width: auto;">
                            <button class="btn">查询</button>
                            <button class="btn btn-success">导出Excel</button>
                        </div>
                    </div>
                    <div class="data-card">
                        <h4>总体数据</h4>
                        <div style="display: flex; justify-content: space-around;">
                            <div>
                                <div style="font-size: 24px; font-weight: bold;">¥128,560</div>
                                <div>总销售额</div>
                            </div>
                            <div>
                                <div style="font-size: 24px; font-weight: bold;">1,256</div>
                                <div>总订单数</div>
                            </div>
                            <div>
                                <div style="font-size: 24px; font-weight: bold;">15</div>
                                <div>活跃经销商</div>
                            </div>
                        </div>
                    </div>
                    <div class="component">
                        <strong>学校销量TOP榜</strong>
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr style="background: #f8f9fa;">
                                    <th style="padding: 10px; border: 1px solid #ddd;">排名</th>
                                    <th style="padding: 10px; border: 1px solid #ddd;">学校名称</th>
                                    <th style="padding: 10px; border: 1px solid #ddd;">销售额</th>
                                    <th style="padding: 10px; border: 1px solid #ddd;">订单数</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td style="padding: 10px; border: 1px solid #ddd;">🥇 1</td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">XX中学</td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">¥45,680</td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">356</td>
                                </tr>
                                <tr>
                                    <td style="padding: 10px; border: 1px solid #ddd;">🥈 2</td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">YY小学</td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">¥32,450</td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">268</td>
                                </tr>
                                <tr>
                                    <td style="padding: 10px; border: 1px solid #ddd;">🥉 3</td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">ZZ高中</td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">¥28,900</td>
                                    <td style="padding: 10px; border: 1px solid #ddd;">195</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能说明 -->
        <div class="section">
            <h2>四、关键功能说明</h2>
            <div class="component">
                <h3>🔄 订单流程</h3>
                <p><strong>预售订单:</strong> 支付定金 → 成团后生产 → 生产完成通知付尾款 → 发货</p>
                <p><strong>现货订单:</strong> 支付全款 → 24小时内发货 → 物流同步</p>
            </div>
            <div class="component">
                <h3>🎯 分销体系</h3>
                <p><strong>经销商:</strong> 可发展下级分销员，设置佣金比例（5%-15%）</p>
                <p><strong>分销员:</strong> 通过分享链接/二维码推广，查看个人业绩</p>
                <p><strong>平台:</strong> 审核分销员资质，监管佣金结算</p>
            </div>
            <div class="component">
                <h3>⚙️ 按钮控制规则</h3>
                <ul>
                    <li><strong>"开始销售":</strong> 商品类型=预售 && 状态=未开始 → 商品在前端可见</li>
                    <li><strong>"导出生产订单":</strong> 订单状态=已支付定金 && 预售已成团 → 生成含尺码数量的Excel</li>
                    <li><strong>"更新生产进度":</strong> 订单状态=生产中 → 用户端显示最新阶段</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
