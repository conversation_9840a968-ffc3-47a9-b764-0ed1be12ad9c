// pages/order-detail/order-detail.js
import orderApi from '../../api/order.js';

Page({
  data: {
    orderId: null,
    order: null,
    loading: true
  },

  onLoad(options) {
    if (options.id) {
      this.setData({ orderId: options.id });
      this.loadOrderDetail();
    }
  },

  onShow() {
    if (this.data.orderId) {
      this.loadOrderDetail();
    }
  },

  // 加载订单详情
  async loadOrderDetail() {
    try {
      this.setData({ loading: true });
      const res = await orderApi.getOrderDetail(this.data.orderId, { mock: true });
      if (res.code === 0) {
        this.setData({
          order: res.data,
          loading: false
        });
        
        // 设置页面标题
        wx.setNavigationBarTitle({
          title: `订单详情 - ${res.data.orderNo}`
        });
      }
    } catch (error) {
      console.error('加载订单详情失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 支付尾款
  async onPayFinalPayment() {
    try {
      wx.showLoading({
        title: '支付中...'
      });

      const res = await orderApi.payFinalPayment(this.data.orderId, { mock: true });
      
      if (res.code === 0) {
        wx.hideLoading();
        wx.showToast({
          title: '支付成功',
          icon: 'success'
        });
        
        // 重新加载订单详情
        setTimeout(() => {
          this.loadOrderDetail();
        }, 1500);
      }
    } catch (error) {
      wx.hideLoading();
      console.error('支付失败:', error);
      wx.showToast({
        title: '支付失败',
        icon: 'none'
      });
    }
  },

  // 确认收货
  async onConfirmReceipt() {
    wx.showModal({
      title: '确认收货',
      content: '确认已收到商品吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            const result = await orderApi.confirmReceipt(this.data.orderId, { mock: true });
            if (result.code === 0) {
              wx.showToast({
                title: '确认收货成功',
                icon: 'success'
              });
              this.loadOrderDetail();
            }
          } catch (error) {
            console.error('确认收货失败:', error);
            wx.showToast({
              title: '操作失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 取消订单
  async onCancelOrder() {
    wx.showModal({
      title: '取消订单',
      content: '确定要取消这个订单吗？',
      success: async (res) => {
        if (res.confirm) {
          try {
            const result = await orderApi.cancelOrder(this.data.orderId, '用户主动取消', { mock: true });
            if (result.code === 0) {
              wx.showToast({
                title: '订单已取消',
                icon: 'success'
              });
              this.loadOrderDetail();
            }
          } catch (error) {
            console.error('取消订单失败:', error);
            wx.showToast({
              title: '操作失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 联系客服
  onContactService() {
    wx.showModal({
      title: '联系客服',
      content: '客服电话：400-123-4567\n工作时间：9:00-18:00',
      showCancel: false
    });
  },

  // 查看物流
  onViewLogistics() {
    if (this.data.order.trackingNo) {
      wx.navigateTo({
        url: `/pages/logistics/logistics?trackingNo=${this.data.order.trackingNo}`
      });
    } else {
      wx.showToast({
        title: '暂无物流信息',
        icon: 'none'
      });
    }
  },

  // 复制订单号
  onCopyOrderNo() {
    wx.setClipboardData({
      data: this.data.order.orderNo,
      success: () => {
        wx.showToast({
          title: '订单号已复制',
          icon: 'success'
        });
      }
    });
  },

  // 获取订单状态文本
  getStatusText(status) {
    const statusMap = {
      'wait_payment': '待付款',
      'paid': '已付款',
      'wait_final_payment': '待付尾款',
      'shipping': '已发货',
      'completed': '已完成',
      'cancelled': '已取消'
    };
    return statusMap[status] || '未知状态';
  },

  // 获取订单状态描述
  getStatusDesc(status) {
    const descMap = {
      'wait_payment': '请尽快完成支付',
      'paid': '商品正在准备中',
      'wait_final_payment': '请支付剩余尾款',
      'shipping': '商品正在配送中',
      'completed': '订单已完成',
      'cancelled': '订单已取消'
    };
    return descMap[status] || '';
  },

  // 获取订单状态图标
  getStatusIcon(status) {
    const iconMap = {
      'wait_payment': '💳',
      'paid': '✅',
      'wait_final_payment': '💰',
      'shipping': '🚚',
      'completed': '🎉',
      'cancelled': '❌'
    };
    return iconMap[status] || '📋';
  },

  // 获取订单状态样式
  getStatusClass(status) {
    const classMap = {
      'wait_payment': 'warning',
      'paid': 'info',
      'wait_final_payment': 'warning',
      'shipping': 'info',
      'completed': 'success',
      'cancelled': 'danger'
    };
    return classMap[status] || 'info';
  },

  // 支付订单
  async onPayOrder() {
    try {
      wx.showLoading({
        title: '支付中...'
      });

      // 这里应该调用支付接口
      // const res = await orderApi.payOrder(this.data.orderId);

      wx.hideLoading();
      wx.showToast({
        title: '支付成功',
        icon: 'success'
      });

      setTimeout(() => {
        this.loadOrderDetail();
      }, 1500);
    } catch (error) {
      wx.hideLoading();
      console.error('支付失败:', error);
      wx.showToast({
        title: '支付失败',
        icon: 'none'
      });
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadOrderDetail().finally(() => {
      wx.stopPullDownRefresh();
    });
  }
});
