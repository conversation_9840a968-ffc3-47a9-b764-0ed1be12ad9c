<!-- pages/order-submit/order-submit.wxml -->
<view class="page">
  <view wx:if="{{loading}}" class="loading-state">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <view wx:else class="order-content">
    <!-- 收货地址卡片 -->
    <view class="section-card address-section" bindtap="onSelectAddress">
      <view class="section-header">
        <view class="header-left">
          <view class="icon-wrapper address-icon">
            <text class="section-icon">📍</text>
          </view>
          <text class="section-title">收货地址</text>
        </view>
        <view class="header-right">
          <text class="action-text">{{address ? '更换' : '选择'}}</text>
          <text class="arrow-icon">→</text>
        </view>
      </view>

      <view wx:if="{{address}}" class="address-content">
        <view class="recipient-info">
          <text class="recipient-name">{{address.name}}</text>
          <text class="recipient-phone">{{address.phone}}</text>
        </view>
        <view class="address-text">{{address.province}}{{address.city}}{{address.district}} {{address.address}}</view>
      </view>

      <view wx:else class="address-empty">
        <view class="empty-content">
          <text class="empty-icon">📍</text>
          <view class="empty-text-group">
            <text class="empty-title">请选择收货地址</text>
            <text class="empty-subtitle">点击添加收货地址</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 商品信息卡片 -->
    <view class="section-card product-section">
      <view class="section-header">
        <view class="header-left">
          <view class="icon-wrapper product-icon">
            <text class="section-icon">📦</text>
          </view>
          <text class="section-title">商品信息</text>
        </view>
      </view>

      <view class="product-list">
        <view class="product-item" wx:for="{{products}}" wx:key="id">
          <image class="product-image" src="{{item.image}}" mode="aspectFill"></image>
          <view class="product-info">
            <view class="product-main">
              <text class="product-name">{{item.name}}</text>
              <view class="product-specs">
                <view class="spec-tag">{{item.size}}</view>
                <view class="spec-tag">×{{item.quantity}}</view>
              </view>
            </view>
            <view class="product-price">
              <view wx:if="{{item.isPreSale}}" class="price-group presale">
                <view class="price-badge">定金</view>
                <text class="price-value">¥{{item.price}}</text>
              </view>
              <view wx:else class="price-group normal">
                <text class="price-value">¥{{item.price}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>



    <!-- 预售协议卡片 -->
    <view class="agreement-card" wx:if="{{hasPresaleProducts}}">
      <view class="card-header">
        <text class="card-icon">📋</text>
        <text class="card-title">预售说明</text>
      </view>

      <view class="agreement-content">
        <view class="agreement-points">
          <text class="agreement-point">• 预售商品需先支付定金，生产完成后支付尾款</text>
          <text class="agreement-point">• 预售周期约15-30个工作日</text>
          <text class="agreement-point">• 定金支付后不可退款，请谨慎下单</text>
        </view>

        <label class="agreement-checkbox">
          <checkbox value="agreement" checked="{{agreementChecked}}" bindchange="onAgreementChange"/>
          <text class="checkbox-text">我已阅读并同意</text>
          <text class="agreement-link" bindtap="onViewAgreement">《预售协议》</text>
        </label>
      </view>
    </view>

    <!-- 费用明细卡片 -->
    <view class="summary-card">
      <view class="card-header">
        <text class="card-icon">💰</text>
        <text class="card-title">费用明细</text>
      </view>

      <view class="summary-content">
        <view class="summary-row">
          <text class="summary-label">商品总价</text>
          <text class="summary-value">¥{{totalAmount}}</text>
        </view>
        <view class="summary-row">
          <text class="summary-label">运费</text>
          <text class="summary-value free">免运费</text>
        </view>
        <view class="summary-divider"></view>
        <view class="summary-total">
          <text class="total-label">实付金额</text>
          <text class="total-amount">¥{{totalAmount}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部提交栏 -->
  <view class="submit-bar" wx:if="{{!loading}}">
    <view class="submit-content">
      <view class="amount-section">
        <view class="amount-row">
          <text class="amount-label">实付金额</text>
          <text class="amount-value">¥{{totalAmount}}</text>
        </view>
      </view>
      <button class="submit-button" bindtap="onSubmitOrder">
        <view class="button-content">
          <text class="button-text">提交订单</text>
          <text class="button-icon">→</text>
        </view>
      </button>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>