<!-- pages/profile/profile.wxml -->
<view class="page">
  <view wx:if="{{loading}}" class="loading-state">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <view wx:else class="profile-content">
    <!-- 用户头部卡片 -->
    <view class="user-header" wx:if="{{userInfo}}">
      <view class="user-background">
        <view class="user-avatar-wrapper">
          <image class="user-avatar" src="{{userInfo.avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
          <view class="avatar-border"></view>
        </view>
        <view class="user-info">
          <text class="user-name">{{userInfo.nickname || '未设置昵称'}}</text>
          <text class="user-phone">{{userInfo.mobile || '未绑定手机号'}}</text>
        </view>
      </view>
    </view>

    <!-- 推广数据卡片 -->
    <view class="stats-card" wx:if="{{promotionData}}">
      <view class="stats-header">
        <text class="stats-title">推广数据</text>
        <text class="stats-subtitle">本月业绩表现</text>
      </view>
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-number">{{promotionData.statistics.totalOrders}}</text>
          <text class="stat-label">推广订单</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">¥{{promotionData.statistics.totalCommission}}</text>
          <text class="stat-label">累计佣金</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{promotionData.statistics.thisMonthOrders}}</text>
          <text class="stat-label">本月订单</text>
        </view>
      </view>
    </view>

    <!-- 快捷功能 -->
    <view class="quick-actions">
      <view class="action-grid">
        <view class="action-item" bindtap="onMyOrders">
          <view class="action-icon-wrapper orders">
            <text class="action-icon">📋</text>
          </view>
          <text class="action-title">我的订单</text>
        </view>

        <view class="action-item" bindtap="onPromotionCode">
          <view class="action-icon-wrapper promotion">
            <text class="action-icon">🎯</text>
          </view>
          <text class="action-title">推广码</text>
        </view>

        <view class="action-item" bindtap="onPromotionPerformance">
          <view class="action-icon-wrapper performance">
            <text class="action-icon">📊</text>
          </view>
          <text class="action-title">推广业绩</text>
        </view>

        <view class="action-item" bindtap="{{userRole === 'dealer' ? 'onViewDealerStatus' : 'onApplyDealer'}}">
          <view class="action-icon-wrapper dealer">
            <text class="action-icon">🏪</text>
          </view>
          <text class="action-title">{{userRole === 'dealer' ? '经销商' : '申请经销商'}}</text>
          <view wx:if="{{dealerStatus === 'pending'}}" class="action-badge warning">审核中</view>
          <view wx:elif="{{dealerStatus === 'approved'}}" class="action-badge success">已认证</view>
          <view wx:elif="{{dealerStatus === 'rejected'}}" class="action-badge danger">已拒绝</view>
        </view>
      </view>
    </view>

    <!-- 经销商工作台入口 -->
    <view class="dealer-entry" wx:if="{{userRole === 'dealer' && dealerStatus === 'approved'}}">
      <view class="dealer-card" bindtap="onDealerDashboard">
        <view class="dealer-content">
          <view class="dealer-icon">💼</view>
          <view class="dealer-text">
            <text class="dealer-title">经销商工作台</text>
            <text class="dealer-subtitle">管理商品、订单和分销</text>
          </view>
        </view>
        <view class="dealer-arrow">→</view>
      </view>
    </view>

    <!-- 菜单列表 -->
    <view class="menu-list">
      <view class="menu-group">
        <view class="menu-item" bindtap="onSettings">
          <view class="menu-icon-wrapper">
            <text class="menu-icon">⚙️</text>
          </view>
          <text class="menu-title">设置</text>
          <text class="menu-arrow">→</text>
        </view>

        <view class="menu-item" bindtap="onContactService">
          <view class="menu-icon-wrapper">
            <text class="menu-icon">📞</text>
          </view>
          <text class="menu-title">联系客服</text>
          <text class="menu-arrow">→</text>
        </view>

        <view class="menu-item" bindtap="onAbout">
          <view class="menu-icon-wrapper">
            <text class="menu-icon">ℹ️</text>
          </view>
          <text class="menu-title">关于我们</text>
          <text class="menu-arrow">→</text>
        </view>
      </view>
    </view>

    <!-- 底部安全区域 -->
    <view class="safe-area-bottom"></view>
  </view>
</view>
