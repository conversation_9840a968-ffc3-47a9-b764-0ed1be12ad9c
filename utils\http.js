// utils/http.js
import { getAccessToken, getRefreshToken } from '../store/user';
import MockUtil from './mock.js';

// 请求队列和正在进行的请求跟踪
let requestList = []; // 请求队列用于刷新令牌时暂存
let isRefreshToken = false; // 是否正在刷新中
let activeRequests = {}; // 跟踪正在进行的请求以防止重复请求
let refreshPromise = null; // 全局刷新令牌的Promise，用于确保只有一个刷新操作

// 自定义请求中间件
const http = {
  fetch: function(config) {
    return this.middleware(config);
  },
  middleware: function(config) {
    // 检查是否启用Mock模式
    if (MockUtil.isMockEnabled(config)) {
      MockUtil.log('Intercepted request for', config.url);
      return MockUtil.generateMockResponse(config).then(response => {
        return this.interceptors.response.success(response, config);
      });
    }

    // 检查是否正在进行令牌刷新，如果是则延迟请求直到刷新完成
    if (isRefreshToken && !config.url.includes('/member/auth/refresh-token')) {
      console.log('Token refresh in progress, delaying request:', config.url);
      return new Promise((resolve) => {
        setTimeout(() => {
          if (!isRefreshToken) {
            console.log('Token refresh completed, proceeding with delayed request:', config.url);
            resolve(this.middleware(config));
          } else {
            resolve(Promise.reject({ code: 401, msg: 'Token refresh still in progress, request delayed' }));
          }
        }, 1000); // 延迟1秒后检查刷新状态
      });
    }
    
    // 检查是否已有相同URL的请求正在进行
    const requestKey = `${config.method}:${config.url}`;
    if (activeRequests[requestKey]) {
      console.log('Duplicate request detected, skipping:', requestKey);
      return Promise.reject({ code: -1, msg: 'Duplicate request skipped' });
    }
    
    // 标记请求为正在进行
    activeRequests[requestKey] = true;
    console.log('New request started:', requestKey);
    
    // Apply request interceptor
    const processedConfig = this.interceptors.request.use(config);
    if (processedConfig instanceof Promise && processedConfig.catch) {
      delete activeRequests[requestKey];
      return processedConfig;
    }
    return new Promise((resolve, reject) => {
      wx.request({
        ...processedConfig,
        success: (response) => {
          delete activeRequests[requestKey];
          console.log('Request completed:', requestKey);
          resolve(this.interceptors.response.success(response, processedConfig));
        },
        fail: (error) => {
          delete activeRequests[requestKey];
          console.log('Request failed:', requestKey);
          reject(this.interceptors.response.error(error, processedConfig));
        }
      });
    });
  },
  interceptors: {
    request: {
      use: function(config) {
        // 自定义处理【auth 授权】：必须登录的接口，如果未登录则静默处理
        if (config.custom.auth && !getAccessToken()) {
          return Promise.reject();
        }

        // 确保 header 对象存在
        if (!config.header) {
          config.header = {};
        }
        // 增加 token 令牌、terminal 终端、tenant 租户的请求头
        const token = getAccessToken();
        if (token) {
          config.header['Authorization'] = `Bearer ${token}`;
          console.log('Token set in headers:', token);
        } else {
          console.log('No token available for headers');
        }
        config.header['terminal'] = 'wechat-mini';
        config.header['Accept'] = '*/*';
        return config;
      }
    },
    response: {
      success: function(response, config) {
        // 约定：如果是 /auth/ 下的 URL 地址，并且返回了 accessToken 说明是登录相关的接口，则自动设置登陆令牌
        if (response.config && response.config.url.indexOf('/member/auth/') >= 0 && response.data?.data?.accessToken) {
          wx.setStorageSync('token', response.data.data.accessToken);
          wx.setStorageSync('refresh-token', response.data.data.refreshToken);
        }

        // 自定义处理【error 错误提示】：如果需要显示错误提示，则显示错误提示
        if (response.data.code !== 0) {
          // 特殊：如果 401 错误码，则跳转到登录页 or 刷新令牌
          if (response.data.code === 401) {
            console.log('401 error detected, attempting token refresh for URL:', config.url);
            return refreshToken(config);
          }
        }
        // 返回结果：包括 code + data + msg
        return response.data;
      },
      error: function(error, config) {
        const isLogin = !!getAccessToken();
        let errorMessage = '网络请求出错';
        if (error !== undefined) {
          switch (error.statusCode) {
            case 400:
              errorMessage = '请求错误';
              break;
            case 401:
              errorMessage = isLogin ? '您的登陆已过期' : '请先登录';
              break;
            case 403:
              errorMessage = '拒绝访问';
              break;
            case 404:
              errorMessage = '请求出错';
              break;
            case 408:
              errorMessage = '请求超时';
              break;
            case 429:
              errorMessage = '请求频繁, 请稍后再访问';
              break;
            case 500:
              errorMessage = '服务器开小差啦,请稍后再试~';
              break;
            case 501:
              errorMessage = '服务未实现';
              break;
            case 502:
              errorMessage = '网络错误';
              break;
            case 503:
              errorMessage = '服务不可用';
              break;
            case 504:
              errorMessage = '网络超时';
              break;
            case 505:
              errorMessage = 'HTTP 版本不受支持';
              break;
          }
          if (error.errMsg.includes('timeout')) errorMessage = '请求超时';
        }

        return false;
      }
    }
  }
};

const refreshToken = async (config) => {
  // 如果当前已经是 refresh-token 的 URL 地址，并且还是 401 错误，说明是刷新令牌失败了，直接返回 Promise.reject(error)
  if (config.url.indexOf('/member/auth/refresh-token') >= 0) {
    return Promise.reject('error');
  }

  // 如果未认证，并且未进行刷新令牌，说明可能是访问令牌过期了
  if (!isRefreshToken) {
    isRefreshToken = true;
    refreshPromise = new Promise(async (resolveRefresh, rejectRefresh) => {
      // 1. 如果获取不到刷新令牌，则只能执行登出操作
      const refreshTokenValue = getRefreshToken();
      if (!refreshTokenValue) {
        rejectRefresh(handleAuthorized());
        return;
      }
      // 2. 进行刷新访问令牌
      try {
        console.log('Attempting to refresh token with value:', refreshTokenValue);
        const refreshTokenResult = await AuthUtil.refreshToken(refreshTokenValue);
        if (refreshTokenResult.code !== 0) {
          console.error('Token refresh failed with code:', refreshTokenResult.code);
          throw new Error('刷新令牌失败');
        }
        console.log('Token refresh successful, new access token:', getAccessToken());
        // 2.1 刷新成功，则只处理当前请求，不回放队列中的旧请求以避免使用过期的token
        config.header.Authorization = 'Bearer ' + getAccessToken();
        resolveRefresh(http.middleware(config));
      } catch (e) {
        console.error('Error during token refresh:', e);
        // 2.2 刷新失败，不回放队列中的请求以避免重复请求
        rejectRefresh(handleAuthorized());
      } finally {
        requestList = [];
        isRefreshToken = false;
        refreshPromise = null;
      }
    });
    return refreshPromise;
  } else {
    // 如果已经在刷新中，则返回现有的刷新Promise以避免重复刷新请求
    console.log('Token refresh already in progress, using existing refresh promise for:', config.url);
    return refreshPromise || Promise.reject({ code: 401, msg: 'Token refresh already in progress, request rejected' });
  }
};

const handleAuthorized = () => {
  wx.removeStorageSync('token');
  wx.removeStorageSync('refresh-token');
  return Promise.reject({
    code: 401,
    msg: getAccessToken() ? '您的登陆已过期' : '请先登录'
  });
};



const AuthUtil = {
  refreshToken: function(refreshToken) {
    console.log('Sending refresh token request to:', getApp().globalData.apiDomain + '/member/auth/refresh-token');
    return http.middleware({
      url: getApp().globalData.apiDomain + '/member/auth/refresh-token?refreshToken=' + encodeURIComponent(refreshToken),
      method: 'POST',
      data: {},
    }).then(response => {
      console.log('Refresh token response received:', response);
      return response;
    }).catch(err => {
      console.error('Error in refresh token request:', err);
      throw err;
    });
  }
};

export default http;
