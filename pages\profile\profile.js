// pages/profile/profile.js
import userApi from '../../api/user.js';
import promotionApi from '../../api/promotion.js';

Page({
  data: {
    userInfo: null,
    promotionData: null,
    userRole: 'customer', // customer: 家长/学生, dealer: 经销商, pending: 申请中
    dealerStatus: 'none', // none: 未申请, pending: 审核中, approved: 已通过, rejected: 已拒绝
    loading: true
  },

  onLoad() {
    this.loadUserInfo();
    this.loadPromotionData();
  },

  onShow() {
    this.loadUserInfo();
  },

  // 加载用户信息
  async loadUserInfo() {
    try {
      const app = getApp();
      const res = await userApi.getUserDetails({ mock: app.globalData.mockMode });
      if (res.code === 0) {
        // 模拟用户角色判断
        const userRole = res.data.role || 'customer';
        const dealerStatus = res.data.dealerStatus || 'none';

        this.setData({
          userInfo: res.data,
          userRole: userRole,
          dealerStatus: dealerStatus,
          loading: false
        });
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
      this.setData({ loading: false });
    }
  },

  // 加载推广数据
  async loadPromotionData() {
    try {
      const app = getApp();
      const res = await promotionApi.getPromotionData({ mock: app.globalData.mockMode });
      if (res.code === 0) {
        this.setData({
          promotionData: res.data
        });
      }
    } catch (error) {
      console.error('加载推广数据失败:', error);
    }
  },

  // 申请成为经销商
  onApplyDealer() {
    if (this.data.dealerStatus === 'pending') {
      wx.showToast({
        title: '申请审核中，请耐心等待',
        icon: 'none'
      });
      return;
    }

    if (this.data.dealerStatus === 'approved') {
      wx.showToast({
        title: '您已是经销商',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/dealer-apply/dealer-apply'
    });
  },

  // 查看申请状态
  onViewDealerStatus() {
    const { dealerStatus } = this.data;
    let content = '';

    switch (dealerStatus) {
      case 'pending':
        content = '您的经销商申请正在审核中，请耐心等待。审核时间通常为3-5个工作日。';
        break;
      case 'approved':
        content = '恭喜！您的经销商申请已通过审核。';
        break;
      case 'rejected':
        content = '很抱歉，您的经销商申请未通过审核。如有疑问，请联系客服。';
        break;
      default:
        content = '您还未申请成为经销商。';
    }

    wx.showModal({
      title: '申请状态',
      content: content,
      showCancel: false
    });
  },

  // 查看我的订单
  onMyOrders() {
    wx.navigateTo({
      url: '/pages/order-list/order-list'
    });
  },

  // 查看推广码
  onPromotionCode() {
    if (!this.data.promotionData) {
      wx.showToast({
        title: '暂无推广数据',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/promotion/promotion'
    });
  },

  // 查看推广业绩
  onPromotionPerformance() {
    wx.navigateTo({
      url: '/pages/promotion-performance/promotion-performance'
    });
  },

  // 进入经销商工作台
  onDealerDashboard() {
    wx.navigateTo({
      url: '/pages/dealer-dashboard/dealer-dashboard'
    });
  },

  // 设置
  onSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    });
  },

  // 联系客服
  onContactService() {
    wx.showModal({
      title: '联系客服',
      content: '客服电话：400-123-4567\n工作时间：9:00-18:00',
      showCancel: false
    });
  },

  // 关于我们
  onAbout() {
    wx.navigateTo({
      url: '/pages/about/about'
    });
  },

  // 分享小程序
  onShareAppMessage() {
    const { promotionData } = this.data;
    return {
      title: '校服小程序 - 优质校服，一站购齐',
      path: `/pages/index/index${promotionData ? '?code=' + promotionData.code : ''}`,
      imageUrl: '/images/share-logo.png'
    };
  }
});
