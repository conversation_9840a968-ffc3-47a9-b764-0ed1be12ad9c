// store/user.js
// 该文件专注于用户认证相关的逻辑操作，提供获取和设置令牌、检查登录状态和登出功能。
// 与 StorageUtil 的区别：StorageUtil 是一个通用的存储管理工具类，负责底层的存储操作，而此文件则在 StorageUtil 基础上构建用户认证相关的业务逻辑。
import StorageUtil from '../utils/storage';

export const getAccessToken = () => {
  return StorageUtil.getToken();
};

export const getRefreshToken = () => {
  return StorageUtil.getRefreshToken();
};

export const setToken = (accessToken, refreshToken) => {
  StorageUtil.setToken(accessToken);
  StorageUtil.setRefreshToken(refreshToken);
};

export const isLogin = () => {
  return !!getAccessToken();
};

export const logout = (force = false) => {
  StorageUtil.clearUserData();
};

/**
 * 获取用户详情，优先从本地存储获取（如果指定 useCache 为 true），否则从 API 获取并缓存
 * @param {boolean} useCache - 是否优先从本地存储获取数据
 * @returns {Promise<Object|null>} 用户详情数据或 null
 */
export const getUserDetails = async (useCache = false) => {
  if (useCache) {
    const cachedDetails = StorageUtil.getUserDetails();
    if (cachedDetails) {
      return cachedDetails;
    }
  }
  try {
    const response = await userApi.getUserDetails();
    if (response && response.data) {
      StorageUtil.setUserDetails(response.data);
      return response.data;
    }
    return null;
  } catch (error) {
    console.error('Failed to fetch user details:', error);
    return null;
  }
};
