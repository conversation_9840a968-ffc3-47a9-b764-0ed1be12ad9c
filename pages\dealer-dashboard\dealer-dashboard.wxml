<!-- pages/dealer-dashboard/dealer-dashboard.wxml -->
<view class="container">
  <view wx:if="{{loading}}" class="loading">
    <text>加载中...</text>
  </view>

  <view wx:else-if="{{dashboardData}}" class="dashboard-content">
    <!-- 认证提醒 -->
    <view class="certification-alert" wx:if="{{dashboardData.certification.status === 'pending'}}">
      <view class="alert-content">
        <text class="alert-icon">⚠️</text>
        <view class="alert-text">
          <text class="alert-title">资质认证</text>
          <text class="alert-message">{{dashboardData.certification.message}}</text>
        </view>
        <button class="alert-btn" bindtap="onCertification">去认证</button>
      </view>
    </view>

    <!-- 今日数据卡片 -->
    <view class="data-card">
      <view class="card-title">今日数据</view>
      <view class="data-stats">
        <view class="stat-item">
          <text class="stat-number">{{dashboardData.todayData.newOrders}}</text>
          <text class="stat-label">新订单</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">¥{{dashboardData.todayData.sales}}</text>
          <text class="stat-label">销售额</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{dashboardData.todayData.presaleRate}}%</text>
          <text class="stat-label">预售达成率</text>
        </view>
      </view>
    </view>

    <!-- 快捷入口 -->
    <view class="quick-actions">
      <view class="section-title">快捷入口</view>
      <view class="action-grid">
        <view class="action-item" bindtap="onProductManage">
          <text class="action-icon">📦</text>
          <text class="action-title">商品管理</text>
        </view>
        <view class="action-item" bindtap="onOrderManage">
          <text class="action-icon">📋</text>
          <text class="action-title">订单处理</text>
        </view>
        <view class="action-item" bindtap="onDistributionManage">
          <text class="action-icon">👥</text>
          <text class="action-title">分销管理</text>
        </view>
        <view class="action-item" bindtap="onDataStatistics">
          <text class="action-icon">📊</text>
          <text class="action-title">数据统计</text>
        </view>
        <view class="action-item" bindtap="onCustomerManage">
          <text class="action-icon">👤</text>
          <text class="action-title">客户管理</text>
        </view>
        <view class="action-item" bindtap="onFinanceManage">
          <text class="action-icon">💰</text>
          <text class="action-title">财务管理</text>
        </view>
      </view>
    </view>

    <!-- 待处理事项 -->
    <view class="pending-tasks">
      <view class="section-title">待处理事项</view>
      <view class="task-list">
        <view class="task-item" wx:if="{{dashboardData.pendingTasks.presaleOrders > 0}}">
          <text class="task-icon">🔄</text>
          <text class="task-text">{{dashboardData.pendingTasks.presaleOrders}}个预售订单待导出生产</text>
        </view>
        <view class="task-item" wx:if="{{dashboardData.pendingTasks.stockOrders > 0}}">
          <text class="task-icon">📦</text>
          <text class="task-text">{{dashboardData.pendingTasks.stockOrders}}个现货订单待发货</text>
        </view>
        <view class="task-item" wx:if="{{dashboardData.pendingTasks.distributorApproval > 0}}">
          <text class="task-icon">👥</text>
          <text class="task-text">{{dashboardData.pendingTasks.distributorApproval}}个分销员申请待审核</text>
        </view>
        <view class="task-item" wx:if="{{dashboardData.pendingTasks.presaleOrders === 0 && dashboardData.pendingTasks.stockOrders === 0 && dashboardData.pendingTasks.distributorApproval === 0}}">
          <text class="task-icon">✅</text>
          <text class="task-text">暂无待处理事项</text>
        </view>
      </view>
    </view>
  </view>
</view>
