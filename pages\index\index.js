// pages/index/index.js
import productApi from '../../api/product.js';

Page({
  data: {
    homeInfo: null,
    products: [],
    cartCount: 0,
    loading: true,
    bannerCurrent: 0
  },

  onLoad(options) {
    this.loadHomeData();
    this.loadProducts();
    this.updateCartCount();
  },

  onShow() {
    this.updateCartCount();
  },

  // 加载首页数据
  async loadHomeData() {
    try {
      const app = getApp();
      const res = await productApi.getHomeInfo({ mock: app.globalData.mockMode });
      if (res.code === 0) {
        this.setData({
          homeInfo: res.data
        });
      }
    } catch (error) {
      console.error('加载首页数据失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 加载商品列表
  async loadProducts() {
    try {
      this.setData({ loading: true });
      const app = getApp();
      const res = await productApi.getProductList({}, { mock: app.globalData.mockMode });
      if (res.code === 0) {
        this.setData({
          products: res.data.list,
          loading: false
        });
      }
    } catch (error) {
      console.error('加载商品列表失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 更新购物车数量
  async updateCartCount() {
    try {
      const app = getApp();
      const res = await productApi.getCartList({ mock: app.globalData.mockMode });
      if (res.code === 0) {
        const count = res.data.list.reduce((sum, item) => sum + item.quantity, 0);
        this.setData({ cartCount: count });
      }
    } catch (error) {
      console.error('获取购物车数量失败:', error);
    }
  },

  // Banner轮播切换
  onBannerChange(e) {
    this.setData({
      bannerCurrent: e.detail.current
    });
  },

  // 点击商品
  onProductTap(e) {
    const productId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/product-detail/product-detail?id=${productId}`
    });
  },

  // 添加到购物车
  async onAddToCart(e) {
    const product = e.currentTarget.dataset.product;

    try {
      const res = await productApi.addToCart({
        productId: product.id,
        quantity: 1,
        size: 'M' // 默认尺码，实际应该让用户选择
      }, { mock: true });

      if (res.code === 0) {
        wx.showToast({
          title: '已加入购物车',
          icon: 'success'
        });
        this.updateCartCount();
      }
    } catch (error) {
      console.error('添加购物车失败:', error);
      wx.showToast({
        title: '添加失败',
        icon: 'none'
      });
    }
  },

  // 跳转到购物车
  onCartTap() {
    wx.navigateTo({
      url: '/pages/cart/cart'
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    Promise.all([
      this.loadHomeData(),
      this.loadProducts()
    ]).finally(() => {
      wx.stopPullDownRefresh();
    });
  }
});
