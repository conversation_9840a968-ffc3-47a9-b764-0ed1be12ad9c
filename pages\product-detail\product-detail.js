// pages/product-detail/product-detail.js
import productApi from '../../api/product.js';

Page({
  data: {
    productId: null,
    product: null,
    selectedSize: '',
    quantity: 1,
    currentImageIndex: 0,
    loading: true
  },

  onLoad(options) {
    if (options.id) {
      this.setData({ productId: options.id });
      this.loadProductDetail();
    }
  },

  // 加载商品详情
  async loadProductDetail() {
    try {
      this.setData({ loading: true });
      const res = await productApi.getProductDetail(this.data.productId, { mock: true });
      if (res.code === 0) {
        this.setData({
          product: res.data,
          loading: false
        });
        // 设置页面标题
        wx.setNavigationBarTitle({
          title: res.data.name
        });
      }
    } catch (error) {
      console.error('加载商品详情失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 图片轮播切换
  onImageChange(e) {
    this.setData({
      currentImageIndex: e.detail.current
    });
  },

  // 选择尺码
  onSizeSelect(e) {
    const size = e.currentTarget.dataset.size;
    this.setData({
      selectedSize: size
    });
  },

  // 数量减少
  onQuantityMinus() {
    if (this.data.quantity > 1) {
      this.setData({
        quantity: this.data.quantity - 1
      });
    }
  },

  // 数量增加
  onQuantityPlus() {
    const maxQuantity = this.getMaxQuantity();
    if (this.data.quantity < maxQuantity) {
      this.setData({
        quantity: this.data.quantity + 1
      });
    }
  },

  // 获取最大可购买数量
  getMaxQuantity() {
    const { product, selectedSize } = this.data;
    if (!product || !selectedSize) return 1;
    
    const sizeInfo = product.sizes.find(s => s.size === selectedSize);
    return sizeInfo ? sizeInfo.stock : 1;
  },

  // 添加到购物车
  async onAddToCart() {
    if (!this.validateSelection()) return;

    try {
      const res = await productApi.addToCart({
        productId: this.data.productId,
        size: this.data.selectedSize,
        quantity: this.data.quantity
      }, { mock: true });

      if (res.code === 0) {
        wx.showToast({
          title: '已加入购物车',
          icon: 'success'
        });
      }
    } catch (error) {
      console.error('添加购物车失败:', error);
      wx.showToast({
        title: '添加失败',
        icon: 'none'
      });
    }
  },

  // 立即购买
  onBuyNow() {
    if (!this.validateSelection()) return;

    // 跳转到订单提交页
    const params = {
      type: 'buy_now',
      productId: this.data.productId,
      size: this.data.selectedSize,
      quantity: this.data.quantity
    };
    
    wx.navigateTo({
      url: `/pages/order-submit/order-submit?${this.objectToQuery(params)}`
    });
  },

  // 验证选择
  validateSelection() {
    if (!this.data.selectedSize) {
      wx.showToast({
        title: '请选择尺码',
        icon: 'none'
      });
      return false;
    }
    
    if (this.data.quantity < 1) {
      wx.showToast({
        title: '请选择数量',
        icon: 'none'
      });
      return false;
    }

    return true;
  },

  // 对象转查询字符串
  objectToQuery(obj) {
    return Object.keys(obj).map(key => `${key}=${encodeURIComponent(obj[key])}`).join('&');
  },

  // 预览图片
  onImagePreview(e) {
    const current = e.currentTarget.dataset.src;
    const urls = this.data.product.images;
    
    wx.previewImage({
      current: current,
      urls: urls
    });
  }
});
