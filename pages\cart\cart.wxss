/* pages/cart/cart.wxss */
.page {
  background-color: var(--background);
  min-height: 100vh;
  padding-bottom: 160rpx;
}

/* 头部操作栏 */
.header-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4);
  background: var(--surface);
  border-bottom: 1rpx solid var(--border-color);
}

.header-left {
  display: flex;
  align-items: baseline;
  gap: var(--space-2);
}

.cart-title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
}

.cart-count {
  font-size: var(--text-sm);
  color: var(--text-muted);
}

.edit-btn {
  font-size: var(--text-base);
  color: var(--primary-color);
  font-weight: 500;
}

/* 内容区域 */
.cart-content {
  flex: 1;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-16) 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--gray-200);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

.loading-text {
  color: var(--text-muted);
  font-size: var(--text-sm);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-16) var(--space-4);
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: var(--space-4);
  opacity: 0.3;
}

.empty-text {
  font-size: var(--text-lg);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.empty-hint {
  font-size: var(--text-sm);
  color: var(--text-muted);
  margin-bottom: var(--space-6);
}

.continue-btn {
  width: 300rpx;
  height: 88rpx;
}

/* 购物车列表 */
.cart-list {
  padding: var(--space-4);
}

.cart-item {
  display: flex;
  align-items: center;
  background: var(--surface);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-3);
  box-shadow: var(--shadow-sm);
  border: 1rpx solid var(--border-color);
}

.item-checkbox {
  margin-right: var(--space-4);
  display: flex;
  align-items: center;
}

.checkbox {
  width: 44rpx;
  height: 44rpx;
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.checkbox.checked {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.check-icon {
  color: var(--text-white);
  font-size: 24rpx;
  font-weight: bold;
}

.item-content {
  flex: 1;
  display: flex;
  align-items: center;
  gap: var(--space-4);
  position: relative;
}

.item-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: var(--radius-md);
  flex-shrink: 0;
}

.item-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 120rpx;
  padding: var(--space-2) 0;
}

.item-name {
  font-size: var(--text-base);
  color: var(--text-primary);
  font-weight: 500;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-specs {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.spec-text {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.presale-tag {
  background: rgba(245, 158, 11, 0.1);
  padding: 2rpx 8rpx;
  border-radius: var(--radius-sm);
}

.tag-text {
  font-size: var(--text-xs);
  color: var(--warning-color);
  font-weight: 500;
}

.item-price-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--space-3);
}

.price-info {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.price-label {
  font-size: var(--text-xs);
  color: var(--warning-color);
  background: rgba(245, 158, 11, 0.1);
  padding: 4rpx 8rpx;
  border-radius: var(--radius-sm);
  font-weight: 500;
}

.item-price {
  font-size: var(--text-xl);
  color: var(--danger-color);
  font-weight: 700;
}

.quantity-controls {
  display: flex;
  align-items: center;
  background: var(--gray-50);
  border-radius: var(--radius-full);
  padding: 4rpx;
}

.quantity-btn {
  width: 56rpx;
  height: 56rpx;
  border: none;
  background: var(--surface);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-lg);
  color: var(--primary-color);
  font-weight: 600;
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
}

.quantity-btn:active {
  transform: scale(0.95);
}

.quantity-btn[disabled] {
  opacity: 0.4;
  background: var(--gray-100);
  color: var(--text-muted);
}

.btn-icon {
  font-weight: 700;
}

.quantity-number {
  min-width: 80rpx;
  text-align: center;
  font-size: var(--text-lg);
  color: var(--text-primary);
  font-weight: 600;
}

.item-delete {
  position: absolute;
  top: 0;
  right: 0;
  padding: var(--space-2);
}

.delete-icon {
  font-size: 32rpx;
  opacity: 0.6;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--surface);
  border-top: 1rpx solid var(--border-color);
  padding: var(--space-4);
  z-index: 1000;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.bar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  min-height: 88rpx;
}

.bar-left {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.select-all {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-right: var(--space-4);
  flex-shrink: 0;
}

.select-text {
  font-size: var(--text-sm);
  color: var(--text-primary);
  font-weight: 500;
  white-space: nowrap;
}

.total-info {
  display: flex;
  align-items: baseline;
  gap: var(--space-1);
  flex: 1;
  min-width: 0;
}

.total-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  white-space: nowrap;
}

.total-amount {
  font-size: var(--text-lg);
  color: var(--danger-color);
  font-weight: 700;
  white-space: nowrap;
}

.bar-right {
  flex-shrink: 0;
}

.checkout-btn,
.delete-btn {
  min-width: 160rpx;
  height: 72rpx;
  font-size: var(--text-sm);
  font-weight: 600;
  border-radius: var(--radius-full);
  flex-shrink: 0;
}

.checkout-btn[disabled] {
  opacity: 0.5;
  background: var(--gray-300) !important;
  color: var(--text-muted) !important;
}

/* 安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
}
