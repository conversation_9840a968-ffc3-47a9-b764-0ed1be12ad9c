# 校服小程序 - 现代化UI设计

## 🎨 设计理念

基于现代化设计系统，打造简洁、大气、美观的用户界面。采用优雅的蓝紫色系作为主色调，配合精心设计的组件系统，为用户提供愉悦的使用体验。

## 🌈 色彩系统

### 主色调
- **主色**: `#6366f1` - 现代紫蓝色，传达专业与信任
- **主色浅色**: `#a5b4fc` - 用于悬停和辅助元素
- **主色深色**: `#4338ca` - 用于按钮按下状态

### 功能色彩
- **成功色**: `#10b981` - 绿色，表示成功状态
- **警告色**: `#f59e0b` - 橙色，表示警告信息
- **危险色**: `#ef4444` - 红色，表示错误或危险操作
- **信息色**: `#3b82f6` - 蓝色，表示信息提示

### 中性色系
采用灰度色阶，从 `gray-50` 到 `gray-900`，确保良好的对比度和可读性。

## 🧩 组件系统

### 按钮组件
- **主要按钮**: 渐变背景，立体阴影效果
- **次要按钮**: 简洁边框，悬停效果
- **幽灵按钮**: 透明背景，彩色边框
- **尺寸变体**: sm, md, lg 三种尺寸

### 卡片组件
- **基础卡片**: 圆角设计，轻微阴影
- **悬浮卡片**: 增强阴影，悬停动效
- **交互卡片**: 点击缩放反馈

### 标签组件
- **状态标签**: 半透明背景，对应状态色彩
- **信息标签**: 圆角设计，易于识别

## 📱 页面设计

### 首页
- **Hero Banner**: 全屏轮播，渐变遮罩，突出品牌形象
- **信息卡片**: 网格布局，图标+文字组合，直观展示关键信息
- **优惠码卡片**: 渐变背景，突出专属优惠
- **商品展示**: 2列网格，卡片式设计，悬浮效果

### 个人中心
- **用户头部**: 渐变背景，头像居中，信息清晰
- **推广数据**: 统计卡片，数据可视化
- **快捷功能**: 4列网格，图标化操作
- **菜单列表**: 简洁列表，清晰层级

### 商品详情
- **图片画廊**: 全屏轮播，指示器显示
- **信息卡片**: 分层展示，价格突出
- **选择组件**: 网格布局，状态清晰
- **操作栏**: 固定底部，双按钮设计

## 🎯 交互设计

### 动画效果
- **按钮反馈**: 点击缩放 + 透明度变化
- **卡片悬浮**: 阴影变化 + 轻微位移
- **页面切换**: 平滑过渡动画
- **加载状态**: 旋转动画 + 渐变效果

### 反馈机制
- **视觉反馈**: 颜色变化、阴影变化
- **触觉反馈**: 点击缩放效果
- **状态提示**: Toast消息、状态标签

## 📐 布局系统

### 间距系统
采用 8px 基础单位，提供 1-16 的间距等级：
- `space-1`: 8rpx
- `space-2`: 16rpx
- `space-3`: 24rpx
- `space-4`: 32rpx
- ...以此类推

### 圆角系统
- `radius-sm`: 8rpx - 小组件
- `radius-md`: 12rpx - 按钮
- `radius-lg`: 16rpx - 卡片
- `radius-xl`: 24rpx - 大卡片
- `radius-full`: 9999rpx - 圆形

### 阴影系统
- `shadow-sm`: 轻微阴影
- `shadow-md`: 中等阴影
- `shadow-lg`: 较强阴影
- `shadow-xl`: 最强阴影

## 🔧 技术实现

### CSS变量系统
使用CSS自定义属性，便于主题切换和维护：
```css
page {
  --primary-color: #6366f1;
  --text-primary: #111827;
  --space-4: 32rpx;
  --radius-lg: 16rpx;
}
```

### 工具类系统
提供丰富的工具类，快速构建界面：
- 文本类: `text-lg`, `text-primary`, `font-bold`
- 间距类: `mt-4`, `px-6`, `py-3`
- 布局类: `flex`, `items-center`, `justify-between`
- 样式类: `rounded-lg`, `shadow-md`

### 响应式设计
- 使用rpx单位，适配不同屏幕尺寸
- 网格布局自适应
- 安全区域适配

## 🚀 使用指南

1. **引入全局样式**: 所有页面自动继承全局样式变量
2. **使用组件类**: 直接使用预定义的组件样式类
3. **工具类组合**: 通过工具类快速调整样式
4. **保持一致性**: 遵循设计系统规范，确保界面一致性

## 📈 优势特点

- **现代化**: 采用最新设计趋势，视觉效果出众
- **一致性**: 统一的设计语言，用户体验连贯
- **可维护**: 组件化设计，易于维护和扩展
- **高性能**: 优化的CSS，流畅的动画效果
- **可访问**: 良好的对比度，清晰的视觉层级

这套UI设计系统为校服小程序提供了专业、现代、美观的视觉体验，同时保持了良好的可用性和可维护性。
