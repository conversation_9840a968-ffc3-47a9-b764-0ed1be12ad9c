// pages/logistics/logistics.js
import orderApi from '../../api/order.js';

Page({
  data: {
    trackingNo: '',
    logistics: null,
    loading: true
  },

  onLoad(options) {
    if (options.trackingNo) {
      this.setData({ trackingNo: options.trackingNo });
      this.loadLogistics();
    }
    
    wx.setNavigationBarTitle({
      title: '物流跟踪'
    });
  },

  // 加载物流信息
  async loadLogistics() {
    try {
      this.setData({ loading: true });
      const app = getApp();
      const res = await orderApi.getLogistics(this.data.trackingNo, { mock: app.globalData.mockMode });
      
      if (res.code === 0) {
        this.setData({
          logistics: res.data,
          loading: false
        });
      }
    } catch (error) {
      console.error('加载物流信息失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 复制运单号
  onCopyTrackingNo() {
    wx.setClipboardData({
      data: this.data.trackingNo,
      success: () => {
        wx.showToast({
          title: '运单号已复制',
          icon: 'success'
        });
      }
    });
  },

  // 联系快递员
  onContactCourier() {
    if (this.data.logistics && this.data.logistics.courierPhone) {
      wx.makePhoneCall({
        phoneNumber: this.data.logistics.courierPhone
      });
    } else {
      wx.showToast({
        title: '暂无快递员联系方式',
        icon: 'none'
      });
    }
  },

  // 刷新物流信息
  onRefresh() {
    this.loadLogistics();
  },

  // 格式化时间
  formatTime(timeStr) {
    const date = new Date(timeStr);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hour = date.getHours().toString().padStart(2, '0');
    const minute = date.getMinutes().toString().padStart(2, '0');
    return `${month}-${day} ${hour}:${minute}`;
  }
});
