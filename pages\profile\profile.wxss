/* pages/profile/profile.wxss */
.page {
  background: linear-gradient(180deg, var(--primary-color) 0%, var(--background) 40%);
  min-height: 100vh;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-16) 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid var(--text-white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

.loading-text {
  color: var(--text-white);
  font-size: var(--text-sm);
}

.profile-content {
  padding: var(--space-6) var(--space-4) 0;
}

/* 用户头部 */
.user-header {
  margin-bottom: var(--space-8);
}

.user-background {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-6) 0;
}

.user-avatar-wrapper {
  position: relative;
  margin-bottom: var(--space-5);
}

.user-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: var(--radius-full);
  border: 6rpx solid rgba(255, 255, 255, 0.2);
}

.avatar-border {
  position: absolute;
  top: -6rpx;
  left: -6rpx;
  right: -6rpx;
  bottom: -6rpx;
  border-radius: var(--radius-full);
  border: 2rpx solid var(--text-white);
  opacity: 0.3;
}

.user-info {
  text-align: center;
  color: var(--text-white);
}

.user-name {
  display: block;
  font-size: var(--text-xl);
  font-weight: 600;
  margin-bottom: var(--space-2);
}

.user-phone {
  display: block;
  font-size: var(--text-base);
  opacity: 0.8;
}

/* 推广数据卡片 */
.stats-card {
  background: var(--surface);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-md);
}

.stats-header {
  text-align: center;
  margin-bottom: var(--space-5);
}

.stats-title {
  display: block;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.stats-subtitle {
  display: block;
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-4);
}

.stat-item {
  text-align: center;
  padding: var(--space-4);
  background: var(--gray-50);
  border-radius: var(--radius-md);
}

.stat-number {
  display: block;
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--space-2);
}

.stat-label {
  display: block;
  font-size: var(--text-xs);
  color: var(--text-muted);
}

/* 快捷功能 */
.quick-actions {
  margin-bottom: var(--space-6);
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--space-4);
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-4);
  background: var(--surface);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  position: relative;
  transition: all 0.2s ease;
}

.action-item:active {
  transform: scale(0.95);
  box-shadow: var(--shadow-md);
}

.action-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-3);
}

.action-icon-wrapper.orders {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.action-icon-wrapper.promotion {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.action-icon-wrapper.performance {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.action-icon-wrapper.dealer {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.action-icon {
  font-size: 36rpx;
  color: var(--text-white);
}

.action-title {
  font-size: var(--text-xs);
  color: var(--text-primary);
  text-align: center;
  font-weight: 500;
}

.action-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  color: var(--text-white);
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: var(--radius-full);
  font-weight: 500;
}

.action-badge.warning {
  background: var(--warning-color);
}

.action-badge.success {
  background: var(--success-color);
}

.action-badge.danger {
  background: var(--danger-color);
}

/* 经销商入口 */
.dealer-entry {
  margin-bottom: var(--space-6);
}

.dealer-card {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-radius: var(--radius-lg);
  padding: var(--space-5);
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: var(--shadow-lg);
  color: var(--text-white);
}

.dealer-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.dealer-icon {
  font-size: 48rpx;
  margin-right: var(--space-4);
}

.dealer-text {
  display: flex;
  flex-direction: column;
}

.dealer-title {
  font-size: var(--text-lg);
  font-weight: 600;
  margin-bottom: var(--space-1);
}

.dealer-subtitle {
  font-size: var(--text-sm);
  opacity: 0.8;
}

.dealer-arrow {
  font-size: var(--text-xl);
  font-weight: bold;
}

/* 菜单列表 */
.menu-list {
  margin-bottom: var(--space-6);
}

.menu-group {
  background: var(--surface);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: var(--space-5);
  border-bottom: 1rpx solid var(--border-color);
  transition: background-color 0.2s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: var(--surface-hover);
}

.menu-icon-wrapper {
  width: 72rpx;
  height: 72rpx;
  background: var(--gray-100);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-4);
}

.menu-icon {
  font-size: 32rpx;
}

.menu-title {
  flex: 1;
  font-size: var(--text-base);
  color: var(--text-primary);
  font-weight: 500;
}

.menu-arrow {
  font-size: var(--text-lg);
  color: var(--text-muted);
}

/* 安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
}
