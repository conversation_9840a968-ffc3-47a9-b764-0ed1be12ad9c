/* pages/dealer-apply/dealer-apply.wxss */
.page {
  background-color: var(--background);
  min-height: 100vh;
  padding-bottom: 160rpx;
}

.apply-content {
  padding: var(--space-4);
}

/* 信息卡片 */
.info-card {
  background: var(--surface);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-6);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.info-header {
  display: flex;
  align-items: center;
  padding: var(--space-5);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--text-white);
}

.info-icon {
  font-size: 32rpx;
  margin-right: var(--space-3);
}

.info-title {
  font-size: var(--text-lg);
  font-weight: 600;
}

.info-content {
  padding: var(--space-5);
}

.info-text {
  display: block;
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-3);
}

.info-text:last-child {
  margin-bottom: 0;
}

/* 表单卡片 */
.form-card {
  background: var(--surface);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-6);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.form-section {
  padding: var(--space-5);
  border-bottom: 1rpx solid var(--border-color);
}

.form-section:last-child {
  border-bottom: none;
}

.section-title {
  display: block;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-5);
}

.form-item {
  margin-bottom: var(--space-5);
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: var(--text-base);
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: var(--space-3);
}

.form-input {
  width: 100%;
  height: 88rpx;
  padding: 0 var(--space-4);
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  color: var(--text-primary);
  background: var(--surface);
}

.form-input:focus {
  border-color: var(--primary-color);
}

.form-textarea {
  width: 100%;
  min-height: 160rpx;
  padding: var(--space-4);
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  color: var(--text-primary);
  background: var(--surface);
  line-height: 1.5;
}

.form-textarea:focus {
  border-color: var(--primary-color);
}

/* 上传区域 */
.upload-section {
  margin-top: var(--space-3);
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200rpx;
  border: 2rpx dashed var(--border-color);
  border-radius: var(--radius-md);
  background: var(--gray-50);
  transition: all 0.2s ease;
}

.upload-placeholder:active {
  border-color: var(--primary-color);
  background: rgba(99, 102, 241, 0.05);
}

.upload-icon {
  font-size: 48rpx;
  margin-bottom: var(--space-2);
  opacity: 0.6;
}

.upload-text {
  font-size: var(--text-sm);
  color: var(--text-muted);
}

.image-preview {
  position: relative;
  width: 100%;
  height: 200rpx;
  border-radius: var(--radius-md);
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
}

.image-actions {
  position: absolute;
  top: var(--space-2);
  right: var(--space-2);
}

.action-btn {
  background: rgba(0, 0, 0, 0.6);
  color: var(--text-white);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
}

/* 提交区域 */
.submit-section {
  padding: var(--space-4) 0;
}

.submit-btn {
  width: 100%;
  height: 96rpx;
  font-size: var(--text-lg);
  font-weight: 600;
}

.submit-btn[disabled] {
  opacity: 0.6;
  background: var(--gray-300) !important;
}

/* 安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom);
}
