// api/product.js
import http from '../utils/http';

const productApi = {
  // 获取首页数据
  getHomeInfo(options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/home/<USER>',
      method: 'GET',
      custom: {
        auth: false,
        mock: options.mock || false
      }
    });
  },

  // 获取商品列表
  getProductList(params = {}, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/products/list',
      method: 'GET',
      data: params,
      custom: {
        auth: false,
        mock: options.mock || false
      }
    });
  },

  // 获取商品详情
  getProductDetail(productId, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/products/detail',
      method: 'GET',
      data: { id: productId },
      custom: {
        auth: false,
        mock: options.mock || false
      }
    });
  },

  // 添加到购物车
  addToCart(data, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/cart/add',
      method: 'POST',
      data: data,
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 获取购物车列表
  getCartList(options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/cart/list',
      method: 'GET',
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 更新购物车商品数量
  updateCartItem(data, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/cart/update',
      method: 'POST',
      data: data,
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  },

  // 删除购物车商品
  removeCartItem(cartId, options = {}) {
    return http.fetch({
      url: getApp().globalData.apiDomain + '/api/cart/remove',
      method: 'POST',
      data: { id: cartId },
      custom: {
        auth: true,
        mock: options.mock || false
      }
    });
  }
};

export default productApi;
