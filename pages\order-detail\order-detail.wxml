<!-- pages/order-detail/order-detail.wxml -->
<view class="page">
  <view wx:if="{{loading}}" class="loading-state">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <view wx:else-if="{{order}}" class="order-detail">
    <!-- 订单状态卡片 -->
    <view class="status-card">
      <view class="status-header">
        <view class="status-icon-wrapper {{getStatusClass(order.status)}}">
          <text class="status-icon">{{getStatusIcon(order.status)}}</text>
        </view>
        <view class="status-info">
          <text class="status-text">{{getStatusText(order.status)}}</text>
          <text class="status-desc">{{getStatusDesc(order.status)}}</text>
        </view>
      </view>

      <!-- 进度条 -->
      <view class="progress-section" wx:if="{{order.status !== 'cancelled'}}">
        <view class="progress-steps">
          <view class="step-item {{order.status === 'wait_payment' || order.status === 'paid' || order.status === 'shipping' || order.status === 'completed' ? 'active' : ''}}">
            <view class="step-dot"></view>
            <text class="step-text">下单</text>
          </view>
          <view class="step-line {{order.status === 'paid' || order.status === 'shipping' || order.status === 'completed' ? 'active' : ''}}"></view>
          <view class="step-item {{order.status === 'paid' || order.status === 'shipping' || order.status === 'completed' ? 'active' : ''}}">
            <view class="step-dot"></view>
            <text class="step-text">{{order.isPreSale ? '生产' : '备货'}}</text>
          </view>
          <view class="step-line {{order.status === 'shipping' || order.status === 'completed' ? 'active' : ''}}"></view>
          <view class="step-item {{order.status === 'shipping' || order.status === 'completed' ? 'active' : ''}}">
            <view class="step-dot"></view>
            <text class="step-text">发货</text>
          </view>
          <view class="step-line {{order.status === 'completed' ? 'active' : ''}}"></view>
          <view class="step-item {{order.status === 'completed' ? 'active' : ''}}">
            <view class="step-dot"></view>
            <text class="step-text">完成</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 收货地址卡片 -->
    <view class="address-card">
      <view class="card-header">
        <text class="card-icon">📍</text>
        <text class="card-title">收货地址</text>
      </view>
      <view class="address-content">
        <view class="address-info">
          <text class="recipient-name">{{order.address.name}}</text>
          <text class="recipient-phone">{{order.address.phone}}</text>
        </view>
        <text class="address-detail">{{order.address.province}} {{order.address.city}} {{order.address.district}} {{order.address.address}}</text>
      </view>
    </view>

    <!-- 商品信息卡片 -->
    <view class="product-card">
      <view class="card-header">
        <text class="card-icon">📦</text>
        <text class="card-title">商品信息</text>
      </view>
      <view class="product-list">
        <view class="product-item" wx:for="{{order.products}}" wx:key="id">
          <image class="product-image" src="{{item.image}}" mode="aspectFill"></image>
          <view class="product-details">
            <text class="product-name">{{item.name}}</text>
            <view class="product-specs">
              <text class="spec-item">尺码: {{item.size}}</text>
              <text class="spec-item">数量: {{item.quantity}}</text>
            </view>
            <view class="product-price">
              <text wx:if="{{item.isPreSale}}" class="price-label">定金</text>
              <text class="price-amount">¥{{item.price}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 订单信息卡片 -->
    <view class="order-info-card">
      <view class="card-header">
        <text class="card-icon">📋</text>
        <text class="card-title">订单信息</text>
      </view>
      <view class="order-info-content">
        <view class="info-row">
          <text class="info-label">订单号</text>
          <view class="info-value-wrapper">
            <text class="info-value">{{order.orderNo}}</text>
            <text class="copy-btn" bindtap="onCopyOrderNo">复制</text>
          </view>
        </view>
        <view class="info-row">
          <text class="info-label">下单时间</text>
          <text class="info-value">{{order.createTime}}</text>
        </view>
        <view class="info-row" wx:if="{{order.payTime}}">
          <text class="info-label">支付时间</text>
          <text class="info-value">{{order.payTime}}</text>
        </view>
        <view class="info-row" wx:if="{{order.trackingNo}}">
          <text class="info-label">物流单号</text>
          <view class="info-value-wrapper">
            <text class="info-value">{{order.trackingNo}}</text>
            <text class="track-btn" bindtap="onViewLogistics">查看物流</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 费用明细卡片 -->
    <view class="summary-card">
      <view class="card-header">
        <text class="card-icon">💰</text>
        <text class="card-title">费用明细</text>
      </view>
      <view class="summary-content">
        <view class="summary-row">
          <text class="summary-label">商品总价</text>
          <text class="summary-value">¥{{order.totalAmount}}</text>
        </view>
        <view class="summary-row">
          <text class="summary-label">运费</text>
          <text class="summary-value free">免运费</text>
        </view>
        <view class="summary-row" wx:if="{{order.paidAmount && order.paidAmount < order.totalAmount}}">
          <text class="summary-label">已付定金</text>
          <text class="summary-value">¥{{order.paidAmount}}</text>
        </view>
        <view class="summary-row" wx:if="{{order.paidAmount && order.paidAmount < order.totalAmount}}">
          <text class="summary-label">待付尾款</text>
          <text class="summary-value highlight">¥{{order.totalAmount - order.paidAmount}}</text>
        </view>
        <view class="summary-divider"></view>
        <view class="summary-total">
          <text class="total-label">{{order.status === 'wait_final_payment' ? '待付金额' : '订单总额'}}</text>
          <text class="total-amount">¥{{order.status === 'wait_final_payment' ? (order.totalAmount - order.paidAmount) : order.totalAmount}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="action-bar" wx:if="{{order}}">
    <view class="action-buttons">
      <button wx:if="{{order.status === 'wait_payment'}}" class="btn btn-secondary" bindtap="onCancelOrder">
        取消订单
      </button>
      <button wx:if="{{order.status === 'wait_payment'}}" class="btn btn-primary" bindtap="onPayOrder">
        立即支付
      </button>

      <button wx:if="{{order.status === 'wait_final_payment'}}" class="btn btn-primary" bindtap="onPayFinalPayment">
        支付尾款
      </button>

      <button wx:if="{{order.status === 'shipping'}}" class="btn btn-secondary" bindtap="onViewLogistics">
        查看物流
      </button>
      <button wx:if="{{order.status === 'shipping'}}" class="btn btn-primary" bindtap="onConfirmReceipt">
        确认收货
      </button>

      <button class="btn btn-ghost" bindtap="onContactService">
        联系客服
      </button>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>